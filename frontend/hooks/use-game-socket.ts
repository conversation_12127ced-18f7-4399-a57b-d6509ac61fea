/**
 * React Hook for Game Socket.IO Connection
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { useAuth } from '@clerk/nextjs';
import { gameSocket, GameEvents, GameState, RoomInfo, ChatMessage } from '@/lib/socketio-client';
import { Chess } from 'chess.js';

export interface UseGameSocketOptions {
  autoConnect?: boolean;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
}

export interface UseGameSocketReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  error: Error | null;

  // Room state
  currentRoom: string | null;
  availableRooms: RoomInfo[];
  gameState: GameState | null;
  chatMessages: ChatMessage[];

  // Chess specific
  chess: Chess;
  isMyTurn: boolean;
  myColor: 'white' | 'black' | null;

  // Actions
  connect: () => Promise<void>;
  disconnect: () => void;
  createRoom: (params: any) => Promise<{ room_id: string }>;
  joinRoom: (roomId: string, joinAs?: 'player' | 'spectator') => Promise<void>;
  makeMove: (from: string, to: string, promotion?: string) => Promise<void>;
  sendMessage: (message: string) => void;
  resign: () => Promise<void>;
  offerDraw: () => Promise<void>;
  acceptDraw: () => Promise<void>;
  placeSpectatorBet: (amount: number, prediction: 'white' | 'black' | 'draw') => Promise<void>;
}

export function useGameSocket(options: UseGameSocketOptions = {}): UseGameSocketReturn {
  const { getToken, userId } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [currentRoom, setCurrentRoom] = useState<string | null>(null);
  const [availableRooms, setAvailableRooms] = useState<RoomInfo[]>([]);
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [myColor, setMyColor] = useState<'white' | 'black' | null>(null);
  const chessRef = useRef(new Chess());

  // Determine if it's the current player's turn
  const isMyTurn = useCallback(() => {
    if (!gameState || !myColor || !userId) return false;

    const currentTurn = gameState.turn;
    const myPlayerId = currentTurn === 'white' ? gameState.white_player : gameState.black_player;

    return myPlayerId === userId;
  }, [gameState, myColor, userId]);

  // Connect to socket server
  const connect = useCallback(async () => {
    if (isConnected || isConnecting) return;

    try {
      setIsConnecting(true);
      setError(null);

      const token = await getToken();
      if (!token || !userId) {
        throw new Error('Authentication required');
      }

      await gameSocket.connect(token, userId);
      setIsConnected(true);
      options.onConnect?.();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Connection failed');
      setError(error);
      options.onError?.(error);
      throw error;
    } finally {
      setIsConnecting(false);
    }
  }, [isConnected, isConnecting, getToken, userId, options]);

  // Disconnect from socket server
  const disconnect = useCallback(() => {
    gameSocket.disconnect();
    setIsConnected(false);
    setCurrentRoom(null);
    setGameState(null);
    setMyColor(null);
    setChatMessages([]);
    options.onDisconnect?.();
  }, [options]);

  // Create a new room
  const createRoom = useCallback(async (params: any) => {
    if (!isConnected) {
      await connect();
    }

    const result = await gameSocket.createRoom(params);
    setCurrentRoom(result.room_id);
    return result;
  }, [isConnected, connect]);

  // Join a room
  const joinRoom = useCallback(async (roomId: string, joinAs: 'player' | 'spectator' = 'player') => {
    if (!isConnected) {
      await connect();
    }

    await gameSocket.joinRoom(roomId, joinAs);
    setCurrentRoom(roomId);
  }, [isConnected, connect]);

  // Make a chess move
  const makeMove = useCallback(async (from: string, to: string, promotion?: string) => {
    if (!currentRoom || !isMyTurn()) {
      throw new Error('Cannot make move');
    }

    await gameSocket.makeMove(from, to, promotion);
  }, [currentRoom, isMyTurn]);

  // Send chat message
  const sendMessage = useCallback((message: string) => {
    if (!currentRoom) return;
    gameSocket.sendMessage(message);
  }, [currentRoom]);

  // Game actions
  const resign = useCallback(async () => {
    if (!currentRoom) throw new Error('Not in a game');
    await gameSocket.resign();
  }, [currentRoom]);

  const offerDraw = useCallback(async () => {
    if (!currentRoom) throw new Error('Not in a game');
    await gameSocket.offerDraw();
  }, [currentRoom]);

  const acceptDraw = useCallback(async () => {
    if (!currentRoom) throw new Error('Not in a game');
    await gameSocket.acceptDraw();
  }, [currentRoom]);

  const placeSpectatorBet = useCallback(async (amount: number, prediction: 'white' | 'black' | 'draw') => {
    if (!currentRoom) throw new Error('Not in a room');
    await gameSocket.placeSpectatorBet(amount, prediction);
  }, [currentRoom]);

  // Setup event listeners
  useEffect(() => {
    if (!isConnected) return;

    // Room events
    const handleRoomsList = (data: { rooms: RoomInfo[] }) => {
      setAvailableRooms(data.rooms);
    };

    const handleRoomsUpdate = (data: { rooms: RoomInfo[] }) => {
      setAvailableRooms(data.rooms);
    };

    // Game events
    const handleGameStarted = (data: any) => {
      setGameState(data.game_state);

      // Determine player color
      if (userId === data.players.white) {
        setMyColor('white');
      } else if (userId === data.players.black) {
        setMyColor('black');
      }

      // Reset chess board
      chessRef.current.reset();
    };

    const handleGameState = (data: { state: GameState }) => {
      setGameState(data.state);
      chessRef.current.load(data.state.board_fen);
    };

    const handleMoveMade = (data: any) => {
      setGameState(data.game_state);
      chessRef.current.load(data.board_fen);
    };

    const handleGameEnded = (data: any) => {
      setGameState(prev => prev ? { ...prev, game_over: true, winner: data.winner, result: data.result } : null);
    };

    const handleClockUpdate = (data: any) => {
      setGameState(prev => prev ? {
        ...prev,
        white_time: data.white_time,
        black_time: data.black_time
      } : null);
    };

    // Chat events
    const handleNewMessage = (message: ChatMessage) => {
      setChatMessages(prev => [...prev, message]);
    };

    // Connection events
    const handleDisconnect = () => {
      setIsConnected(false);
      options.onDisconnect?.();
    };

    const handleConnectError = (error: Error) => {
      setError(error);
      options.onError?.(error);
    };

    // Register event handlers
    gameSocket.on('rooms_list', handleRoomsList);
    gameSocket.on('rooms_update', handleRoomsUpdate);
    gameSocket.on('game_started', handleGameStarted);
    gameSocket.on('game_state', handleGameState);
    gameSocket.on('move_made', handleMoveMade);
    gameSocket.on('game_ended', handleGameEnded);
    gameSocket.on('clock_update', handleClockUpdate);
    gameSocket.on('new_message', handleNewMessage);
    gameSocket.on('disconnect', handleDisconnect);
    gameSocket.on('connect_error', handleConnectError);

    // Cleanup
    return () => {
      gameSocket.off('rooms_list', handleRoomsList);
      gameSocket.off('rooms_update', handleRoomsUpdate);
      gameSocket.off('game_started', handleGameStarted);
      gameSocket.off('game_state', handleGameState);
      gameSocket.off('move_made', handleMoveMade);
      gameSocket.off('game_ended', handleGameEnded);
      gameSocket.off('clock_update', handleClockUpdate);
      gameSocket.off('new_message', handleNewMessage);
      gameSocket.off('disconnect', handleDisconnect);
      gameSocket.off('connect_error', handleConnectError);
    };
  }, [isConnected, userId, options]);

  // Auto-connect on mount if requested
  useEffect(() => {
    if (options.autoConnect && !isConnected && !isConnecting) {
      connect().catch(console.error);
    }
  }, [options.autoConnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isConnected) {
        disconnect();
      }
    };
  }, []);

  return {
    // Connection state
    isConnected,
    isConnecting,
    error,

    // Room state
    currentRoom,
    availableRooms,
    gameState,
    chatMessages,

    // Chess specific
    chess: chessRef.current,
    isMyTurn: isMyTurn(),
    myColor,

    // Actions
    connect,
    disconnect,
    createRoom,
    joinRoom,
    makeMove,
    sendMessage,
    resign,
    offerDraw,
    acceptDraw,
    placeSpectatorBet
  };
}