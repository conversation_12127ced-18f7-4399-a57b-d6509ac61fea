"""
Simple Clerk authentication stub for local development
"""

import os
from typing import Op<PERSON>, Dict, Any
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

security = HTTPBearer()

# Clerk configuration
CLERK_DOMAIN = os.getenv("CLERK_DOMAIN", "betbet.com")
CLERK_SECRET_KEY = os.getenv("CLERK_SECRET_KEY")
CLERK_WEBHOOK_SECRET = os.getenv("CLERK_WEBHOOK_SECRET")


class ClerkUser:
    """Represents a Clerk user with BetBet metadata"""

    def __init__(self, clerk_data: Dict[str, Any]):
        self.id = clerk_data.get("sub", "test_user")  # Clerk user ID
        self.email = clerk_data.get("email", "<EMAIL>")
        self.username = clerk_data.get("username", "testuser")
        self.first_name = clerk_data.get("first_name", "Test")
        self.last_name = clerk_data.get("last_name", "User")
        self.image_url = clerk_data.get("image_url", "")
        self.created_at = clerk_data.get("created_at", 0)
        self.updated_at = clerk_data.get("updated_at", 0)
        self.metadata = clerk_data.get("public_metadata", {})
        self.private_metadata = clerk_data.get("private_metadata", {})
        self.unsafe_metadata = clerk_data.get("unsafe_metadata", {})


async def verify_clerk_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> ClerkUser:
    """
    Simplified token verification for local development
    In production, this will verify with Clerk
    """
    # For now, return a test user
    test_user_data = {
        "sub": "test_user_123",
        "email": "<EMAIL>",
        "username": "testuser",
        "first_name": "Test",
        "last_name": "User",
    }
    return ClerkUser(test_user_data)


async def get_current_user(
    user: ClerkUser = Depends(verify_clerk_token),
) -> ClerkUser:
    """Get the current authenticated user"""
    return user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
) -> Optional[ClerkUser]:
    """Get the current authenticated user (optional - can be None)"""
    if not credentials:
        return None
    test_user_data = {
        "sub": "optional_user_123",
        "email": "<EMAIL>",
        "username": "optionaluser",
    }
    return ClerkUser(test_user_data)


async def require_admin(
    user: ClerkUser = Depends(get_current_user),
) -> ClerkUser:
    """Require admin role for the current user"""
    # For local development, always allow
    return user


def require_kyc_level(level: int):
    """Require specific KYC level for the current user"""
    def kyc_dependency(user: ClerkUser = Depends(get_current_user)) -> ClerkUser:
        # For local development, always allow
        return user
    return kyc_dependency


async def verify_websocket_token(token: str) -> Optional[ClerkUser]:
    """Verify WebSocket authentication token"""
    if not token:
        return None
    test_user_data = {
        "sub": f"ws_user_{token[:8]}",
        "email": "<EMAIL>",
        "username": "wsuser",
    }
    return ClerkUser(test_user_data)


def require_market_creation_permission():
    """Require market creation permission"""
    def market_dependency(user: ClerkUser = Depends(get_current_user)) -> ClerkUser:
        # For local development, always allow
        return user
    return market_dependency


def require_expert_status(user: ClerkUser = Depends(get_current_user)) -> ClerkUser:
    """Require expert status for analysis creation"""
    # For local development, always allow
    print(f"[LOCAL DEV] Expert status check for user {user.id}: Approved")
    return user


def verify_webhook_signature(payload: bytes, headers: Dict[str, str]) -> bool:
    """
    Verify Clerk webhook signature
    For local development, always return True
    """
    return True


async def update_user_metadata(user_id: str, public_metadata: Dict[str, Any] = None,
                             private_metadata: Dict[str, Any] = None):
    """Update user metadata in Clerk (stub for local development)"""
    print(f"[LOCAL DEV] Would update user {user_id} metadata:")
    if public_metadata:
        print(f"  Public: {public_metadata}")
    if private_metadata:
        print(f"  Private: {private_metadata}")


async def is_admin(user_id: str) -> bool:
    """Check if user has admin privileges (stub for local development)"""
    # For local development, always return True
    print(f"[LOCAL DEV] Admin check for user {user_id}: True")
    return True


class WebSocketAuth:
    """WebSocket authentication manager"""

    @staticmethod
    async def authenticate(token: str) -> Optional[ClerkUser]:
        """
        Authenticate WebSocket connection
        For local development, return test user
        """
        test_user_data = {
            "sub": f"ws_user_{token[:8]}",
            "email": "<EMAIL>",
            "username": "wsuser",
        }
        return ClerkUser(test_user_data)