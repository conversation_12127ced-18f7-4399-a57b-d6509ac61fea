"""
BetBet Multi-Currency Wallet Service
Handles deposits, withdrawals, P2P transfers, and payment methods with Stripe integration
"""

import os
import uuid
import stripe
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from decimal import Decimal
from dotenv import load_dotenv

from fastapi import FastAPI, Depends, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func, desc
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, validator
import logging

# Load environment variables
load_dotenv()

import sys
sys.path.append('../..')
from shared.database import (
    init_all_databases, close_all_databases, get_db,
    get_redis_client
)
from shared.clerk_auth_simple import (
    ClerkUser, get_current_user, get_current_user_optional,
    require_kyc_level
)
from shared.models import (
    UserProfile, Wallet, Transaction
)

# Stripe configuration
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if not stripe.api_key:
    logger.warning("STRIPE_SECRET_KEY not found in environment variables")
else:
    logger.info(f"Stripe configured with key: {stripe.api_key[:12]}...")

# Pydantic models for request/response
class WalletCreate(BaseModel):
    currency: str
    wallet_type: str = "fiat"  # 'fiat' or 'crypto'

class DepositRequest(BaseModel):
    amount: Decimal
    currency: str
    payment_method: str
    payment_details: Optional[Dict[str, Any]] = None

class WithdrawalRequestCreate(BaseModel):
    wallet_id: str
    amount: Decimal
    payment_method: str

class P2PTransferRequest(BaseModel):
    recipient_email: str
    amount: Decimal
    currency: str
    memo: Optional[str] = None

class WalletResponse(BaseModel):
    id: str
    currency: str
    balance: Decimal
    available_balance: Decimal
    locked_balance: Decimal
    wallet_type: str
    is_default: bool

class TransactionResponse(BaseModel):
    id: str
    type: str
    amount: Decimal
    currency: str
    fee: Decimal
    status: str
    reference_id: Optional[str]
    created_at: datetime
    completed_at: Optional[datetime]

# Lifespan manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Starting Multi-Currency Wallet Service...")
    await init_all_databases()
    yield
    print("Shutting down Multi-Currency Wallet Service...")
    await close_all_databases()

# Create FastAPI app
app = FastAPI(
    title="BetBet Multi-Currency Wallet Service",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Helper functions
async def get_user_wallet(db: AsyncSession, clerk_user_id: str, currency: str) -> Optional[Wallet]:
    """Get user's wallet for specific currency"""
    result = await db.execute(
        select(Wallet).where(
            and_(Wallet.clerk_user_id == clerk_user_id, Wallet.currency == currency)
        )
    )
    return result.scalar_one_or_none()

async def create_wallet_if_not_exists(db: AsyncSession, clerk_user_id: str, currency: str) -> Wallet:
    """Create wallet if it doesn't exist"""
    wallet = await get_user_wallet(db, clerk_user_id, currency)
    if not wallet:
        wallet = Wallet(
            clerk_user_id=clerk_user_id,
            currency=currency,
            balance=Decimal('0'),
            available_balance=Decimal('0'),
            locked_balance=Decimal('0'),
            wallet_type='fiat' if currency in ['USD', 'ZWL', 'ZAR'] else 'crypto',
            is_default=(currency == 'USD')
        )
        db.add(wallet)
        await db.commit()
        await db.refresh(wallet)
    return wallet

async def update_wallet_balance(
    db: AsyncSession,
    wallet_id: str,
    amount: Decimal,
    operation: str = 'add'
):
    """Update wallet balance"""
    wallet = await db.get(Wallet, wallet_id)
    if not wallet:
        raise HTTPException(status_code=404, detail="Wallet not found")

    if operation == 'add':
        wallet.balance += amount
        wallet.available_balance += amount
    elif operation == 'subtract':
        if wallet.available_balance < amount:
            raise HTTPException(status_code=400, detail="Insufficient balance")
        wallet.balance -= amount
        wallet.available_balance -= amount
    elif operation == 'lock':
        if wallet.available_balance < amount:
            raise HTTPException(status_code=400, detail="Insufficient available balance")
        wallet.available_balance -= amount
        wallet.locked_balance += amount
    elif operation == 'unlock':
        wallet.available_balance += amount
        wallet.locked_balance -= amount

    await db.commit()
    return wallet

# Wallet Management Endpoints
@app.get("/api/v1/wallets", response_model=List[WalletResponse])
async def get_user_wallets(
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all user wallets"""
    result = await db.execute(
        select(Wallet).where(Wallet.clerk_user_id == current_user.id).order_by(desc(Wallet.is_default))
    )
    wallets = result.scalars().all()

    return [
        WalletResponse(
            id=str(wallet.id),
            currency=wallet.currency,
            balance=wallet.balance,
            available_balance=wallet.available_balance,
            locked_balance=wallet.locked_balance,
            wallet_type=wallet.wallet_type,
            is_default=wallet.is_default
        )
        for wallet in wallets
    ]

@app.post("/api/v1/wallets/create")
async def create_wallet(
    wallet_data: WalletCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new wallet for user"""
    wallet = await create_wallet_if_not_exists(
        db, current_user.id, wallet_data.currency
    )

    return {
        "id": str(wallet.id),
        "currency": wallet.currency,
        "balance": wallet.balance,
        "wallet_type": wallet.wallet_type,
        "message": "Wallet created successfully"
    }

@app.get("/api/v1/wallets/{wallet_id}/balance")
async def get_wallet_balance(
    wallet_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get specific wallet balance"""
    wallet = await db.get(Wallet, wallet_id)
    if not wallet:
        raise HTTPException(status_code=404, detail="Wallet not found")

    if wallet.clerk_user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return {
        "wallet_id": wallet_id,
        "currency": wallet.currency,
        "balance": wallet.balance,
        "available_balance": wallet.available_balance,
        "locked_balance": wallet.locked_balance
    }

# Deposit Endpoints
@app.post("/api/v1/deposits/initiate")
async def initiate_deposit(
    deposit_data: DepositRequest,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Initiate deposit using Stripe or other payment methods"""

    # Create wallet if it doesn't exist
    wallet = await create_wallet_if_not_exists(
        db, current_user.id, deposit_data.currency
    )

    # Create transaction record
    transaction = Transaction(
        clerk_user_id=current_user.id,
        wallet_id=wallet.id,
        type='deposit',
        amount=deposit_data.amount,
        currency=deposit_data.currency,
        status='pending',
        payment_method=deposit_data.payment_method,
        payment_details=deposit_data.payment_details or {}
    )
    db.add(transaction)
    await db.commit()
    await db.refresh(transaction)

    # Handle different payment methods
    if deposit_data.payment_method == 'stripe':
        try:
            # Create Stripe Payment Intent
            intent = stripe.PaymentIntent.create(
                amount=int(deposit_data.amount * 100),  # Convert to cents
                currency=deposit_data.currency.lower(),
                metadata={
                    'user_id': current_user.id,
                    'transaction_id': str(transaction.id),
                    'wallet_id': str(wallet.id)
                }
            )

            transaction.reference_id = intent.id
            await db.commit()

            return {
                "transaction_id": str(transaction.id),
                "client_secret": intent.client_secret,
                "status": "pending_payment",
                "amount": deposit_data.amount,
                "currency": deposit_data.currency,
                "stripe_publishable_key": STRIPE_PUBLISHABLE_KEY
            }

        except stripe.error.StripeError as e:
            transaction.status = 'failed'
            await db.commit()
            raise HTTPException(status_code=400, detail=f"Payment failed: {str(e)}")

    elif deposit_data.payment_method in ['ecocash', 'onemoney']:
        # For mobile money, return payment instructions
        return {
            "transaction_id": str(transaction.id),
            "status": "pending_confirmation",
            "payment_instructions": f"Send {deposit_data.amount} {deposit_data.currency} to {deposit_data.payment_method} number: +263123456789",
            "reference": str(transaction.id)[:8].upper()
        }

    return {
        "transaction_id": str(transaction.id),
        "status": "pending",
        "message": "Deposit initiated successfully"
    }

@app.post("/api/v1/deposits/confirm")
async def confirm_deposit(
    deposit_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Confirm deposit (for manual confirmation methods like mobile money)"""

    transaction = await db.get(Transaction, deposit_id)
    if not transaction or transaction.clerk_user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Transaction not found")

    if transaction.type != 'deposit':
        raise HTTPException(status_code=400, detail="Not a deposit transaction")

    # For now, auto-approve (in production, this would require admin approval)
    transaction.status = 'completed'
    transaction.completed_at = datetime.utcnow()

    # Update wallet balance
    await update_wallet_balance(
        db, str(transaction.wallet_id), transaction.amount, 'add'
    )

    await db.commit()

    return {
        "transaction_id": deposit_id,
        "status": "completed",
        "message": "Deposit confirmed and credited to wallet"
    }

# Withdrawal Endpoints
@app.post("/api/v1/withdrawals/request")
async def request_withdrawal(
    withdrawal_data: WithdrawalRequestCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Request withdrawal"""

    wallet = await db.get(Wallet, withdrawal_data.wallet_id)
    if not wallet or wallet.clerk_user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Wallet not found")

    if wallet.available_balance < withdrawal_data.amount:
        raise HTTPException(status_code=400, detail="Insufficient balance")

    # Calculate fee (2% for now)
    fee = withdrawal_data.amount * Decimal('0.02')

    # Create transaction record
    transaction = Transaction(
        clerk_user_id=current_user.id,
        wallet_id=withdrawal_data.wallet_id,
        type='withdrawal',
        amount=withdrawal_data.amount,
        currency=wallet.currency,
        fee=fee,
        status='pending',
        payment_method=withdrawal_data.payment_method
    )
    db.add(transaction)

    # Lock the funds
    await update_wallet_balance(
        db, withdrawal_data.wallet_id, withdrawal_data.amount + fee, 'lock'
    )

    await db.commit()

    return {
        "transaction_id": str(transaction.id),
        "amount": withdrawal_data.amount,
        "fee": fee,
        "status": "pending_review",
        "message": "Withdrawal request submitted for review"
    }

# P2P Transfer Endpoints
@app.post("/api/v1/transfers/send")
async def send_p2p_transfer(
    transfer_data: P2PTransferRequest,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Send P2P transfer to another user"""

    # Find recipient by email
    result = await db.execute(
        select(UserProfile).where(UserProfile.email == transfer_data.recipient_email)
    )
    recipient = result.scalar_one_or_none()
    if not recipient:
        raise HTTPException(status_code=404, detail="Recipient not found")

    # Get sender's wallet
    sender_wallet = await get_user_wallet(db, current_user.id, transfer_data.currency)
    if not sender_wallet:
        raise HTTPException(status_code=404, detail="Wallet not found")

    if sender_wallet.available_balance < transfer_data.amount:
        raise HTTPException(status_code=400, detail="Insufficient balance")

    # Get or create recipient wallet
    recipient_wallet = await create_wallet_if_not_exists(
        db, recipient.clerk_user_id, transfer_data.currency
    )

    # Create transactions for both parties
    sender_transaction = Transaction(
        clerk_user_id=current_user.id,
        wallet_id=sender_wallet.id,
        type='transfer',
        amount=-transfer_data.amount,
        currency=transfer_data.currency,
        status='completed',
        reference_type='p2p',
        completed_at=datetime.utcnow()
    )

    recipient_transaction = Transaction(
        clerk_user_id=recipient.clerk_user_id,
        wallet_id=recipient_wallet.id,
        type='transfer',
        amount=transfer_data.amount,
        currency=transfer_data.currency,
        status='completed',
        reference_type='p2p',
        completed_at=datetime.utcnow()
    )

    db.add(sender_transaction)
    db.add(recipient_transaction)

    # Update balances
    await update_wallet_balance(db, str(sender_wallet.id), transfer_data.amount, 'subtract')
    await update_wallet_balance(db, str(recipient_wallet.id), transfer_data.amount, 'add')

    await db.commit()

    return {
        "recipient_email": transfer_data.recipient_email,
        "amount": transfer_data.amount,
        "currency": transfer_data.currency,
        "status": "completed",
        "message": "Transfer sent successfully"
    }

# Transaction History Endpoints
@app.get("/api/v1/transactions", response_model=List[TransactionResponse])
async def get_transactions(
    transaction_type: Optional[str] = None,
    limit: int = 50,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user transaction history"""

    query = select(Transaction).where(Transaction.clerk_user_id == current_user.id)

    if transaction_type:
        query = query.where(Transaction.type == transaction_type)

    query = query.order_by(desc(Transaction.created_at)).limit(limit)

    result = await db.execute(query)
    transactions = result.scalars().all()

    return [
        TransactionResponse(
            id=str(tx.id),
            type=tx.type,
            amount=tx.amount,
            currency=tx.currency,
            fee=tx.fee or Decimal('0'),
            status=tx.status,
            reference_id=tx.reference_id,
            created_at=tx.created_at,
            completed_at=tx.completed_at
        )
        for tx in transactions
    ]

# Stripe Webhook
@app.post("/api/v1/webhooks/stripe")
async def stripe_webhook(request: Request, db: AsyncSession = Depends(get_db)):
    """Handle Stripe webhook events"""
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')

    if not STRIPE_WEBHOOK_SECRET:
        logger.warning("STRIPE_WEBHOOK_SECRET not configured")
        raise HTTPException(status_code=400, detail="Webhook secret not configured")

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        logger.error("Invalid payload")
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Handle the event
    if event['type'] == 'payment_intent.succeeded':
        payment_intent = event['data']['object']
        await handle_payment_success(db, payment_intent)
    elif event['type'] == 'payment_intent.payment_failed':
        payment_intent = event['data']['object']
        await handle_payment_failure(db, payment_intent)
    else:
        logger.info(f"Unhandled event type: {event['type']}")

    return {"status": "success"}

async def handle_payment_success(db: AsyncSession, payment_intent: dict):
    """Handle successful Stripe payment"""
    stripe_payment_id = payment_intent['id']

    # Find the transaction
    result = await db.execute(
        select(Transaction).where(Transaction.reference_id == stripe_payment_id)
    )
    transaction = result.scalar_one_or_none()

    if not transaction:
        logger.error(f"Transaction not found for payment intent: {stripe_payment_id}")
        return

    if transaction.status == 'completed':
        logger.info(f"Transaction {transaction.id} already completed")
        return

    # Update transaction status
    transaction.status = 'completed'
    transaction.completed_at = datetime.utcnow()
    transaction.stripe_payment_intent_id = stripe_payment_id

    # Update wallet balance
    await update_wallet_balance(
        db, str(transaction.wallet_id), transaction.amount, 'add'
    )

    await db.commit()
    logger.info(f"Payment completed for transaction {transaction.id}")

async def handle_payment_failure(db: AsyncSession, payment_intent: dict):
    """Handle failed Stripe payment"""
    stripe_payment_id = payment_intent['id']

    # Find the transaction
    result = await db.execute(
        select(Transaction).where(Transaction.reference_id == stripe_payment_id)
    )
    transaction = result.scalar_one_or_none()

    if not transaction:
        logger.error(f"Transaction not found for payment intent: {stripe_payment_id}")
        return

    # Update transaction status
    transaction.status = 'failed'
    transaction.stripe_payment_intent_id = stripe_payment_id

    await db.commit()
    logger.info(f"Payment failed for transaction {transaction.id}")

# Health check
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "wallet"}

# Test endpoint to create sample wallets
@app.post("/api/v1/test/create-sample-wallets")
async def create_sample_wallets(
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create sample wallets for testing"""
    currencies = ['USD', 'ZWL', 'ZAR']
    wallets_created = []

    for currency in currencies:
        wallet = await create_wallet_if_not_exists(db, current_user.id, currency)
        wallets_created.append({
            "id": str(wallet.id),
            "currency": currency,
            "balance": wallet.balance
        })

    return {
        "message": "Sample wallets created",
        "wallets": wallets_created
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)