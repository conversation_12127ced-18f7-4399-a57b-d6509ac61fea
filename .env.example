# BetBet Platform Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# Clerk Authentication (Required)
# Get these from your Clerk Dashboard: https://dashboard.clerk.dev
# =============================================================================

CLERK_PUBLISHABLE_KEY=pk_test_xxx
CLERK_SECRET_KEY=sk_test_xxx
CLERK_WEBHOOK_SECRET=whsec_xxx
CLERK_JWT_VERIFICATION_KEY="-----BEGIN PUBLIC KEY-----xxx-----END PUBLIC KEY-----"
CLERK_DOMAIN=localhost:3000

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL (Primary database)
DATABASE_URL=postgresql+asyncpg://betbet:betbet@localhost:5432/betbet

# MongoDB (Game states, logs, analytics)
MONGODB_URL=**********************************************

# Redis (Caching, sessions, real-time)
REDIS_URL=redis://localhost:6379

# Optional: TimescaleDB for time-series data
TIMESCALE_URL=postgresql://betbet:betbet@localhost:5433/betbet_timeseries

# Optional: Cassandra for high-write scenarios
CASSANDRA_HOSTS=localhost

# Optional: Elasticsearch for advanced search
ELASTICSEARCH_URL=http://localhost:9200

# =============================================================================
# Payment Processing
# =============================================================================

# Stripe (Required for card payments)
STRIPE_SECRET_KEY=sk_test_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
STRIPE_CONNECT_CLIENT_ID=ca_xxx

# Cryptocurrency (Optional)
BITCOIN_RPC_URL=http://localhost:8332
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/xxx
LIGHTNING_NETWORK_URL=https://your-lightning-node.com

# Mobile Money (Optional - for African markets)
ECOCASH_API_KEY=xxx
ONEMONEY_API_KEY=xxx

# =============================================================================
# AI & Analytics
# =============================================================================

# OpenAI (Required for expert analysis features)
OPENAI_API_KEY=sk-xxx

# Optional: Other AI services
ANTHROPIC_API_KEY=sk-ant-xxx
HUGGING_FACE_API_KEY=hf_xxx

# =============================================================================
# External APIs
# =============================================================================

# Sports data (Optional)
ODDS_API_KEY=xxx
ESPN_API_KEY=xxx

# Social media integration (Optional)
TWITTER_API_KEY=xxx
FACEBOOK_API_KEY=xxx

# =============================================================================
# Service Configuration
# =============================================================================

# Environment
NODE_ENV=development
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# Service URLs (for microservices communication)
USER_PROFILE_SERVICE_URL=http://localhost:8000
GAME_ENGINE_SERVICE_URL=http://localhost:8001
BETTING_MARKET_SERVICE_URL=http://localhost:8002
ANALYTICS_SERVICE_URL=http://localhost:8003
WALLET_SERVICE_URL=http://localhost:8004
SOCIAL_FORUM_SERVICE_URL=http://localhost:8005

# API Gateway
API_GATEWAY_URL=http://localhost:80
KONG_ADMIN_URL=http://localhost:8001

# =============================================================================
# Security & Rate Limiting
# =============================================================================

# JWT Secrets (generate random strings)
JWT_SECRET=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=50

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# Monitoring & Observability (Optional)
# =============================================================================

# Prometheus
PROMETHEUS_PORT=9090

# Grafana
GRAFANA_ADMIN_PASSWORD=betbet

# Sentry (Error tracking)
SENTRY_DSN=https://<EMAIL>/xxx

# =============================================================================
# Development & Testing
# =============================================================================

# Debug flags
DEBUG=true
VERBOSE_LOGGING=true

# Test database (separate from main DB)
TEST_DATABASE_URL=postgresql+asyncpg://betbet:betbet@localhost:5432/betbet_test

# =============================================================================
# Production Overrides
# =============================================================================

# When deploying to production, override these:
# CLERK_DOMAIN=betbet.com
# NODE_ENV=production
# ENVIRONMENT=production
# LOG_LEVEL=INFO
# CORS_ORIGINS=https://betbet.com,https://www.betbet.com
# DEBUG=false