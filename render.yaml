# BetBet Platform — Render Blueprint (fixed)

services:
  # ──────────────────────────────
  # Redis (managed)
  # ──────────────────────────────
  - type: redis
    name: betbet-redis
    plan: free
    ipAllowList: []   # private by default

  # ──────────────────────────────
  # Frontend (Next.js)
  # ──────────────────────────────
  - type: web
    name: betbet-frontend
    env: node
    plan: free
    region: oregon
    buildCommand: |
      cd frontend
      npm ci
      npm run build
    startCommand: |
      cd frontend
      npm start
    envVars:
      - key: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
        sync: false
      - key: CLERK_SECRET_KEY
        sync: false
      - key: NODE_ENV
        value: production

  # ──────────────────────────────
  # User Profile Service (FastAPI)
  # ──────────────────────────────
  - type: web
    name: betbet-user-profile
    env: python
    plan: free
    region: oregon
    buildCommand: |
      cd backend
      pip install -r requirements.txt
    startCommand: |
      cd backend/services/user_profile
      PYTHONPATH=../.. uvicorn main:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: betbet-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: betbet-redis
          property: connectionString
      - key: CLERK_SECRET_KEY
        sync: false
      - key: CLERK_WEBHOOK_SECRET
        sync: false
      - key: CLERK_DOMAIN
        value: betbet.co.zw
      - key: MONGODB_URL
        sync: false

  # ──────────────────────────────
  # Game Engine (FastAPI)
  # ──────────────────────────────
  - type: web
    name: betbet-game-engine
    env: python
    plan: free
    region: oregon
    buildCommand: |
      cd backend
      pip install -r requirements.txt
    startCommand: |
      cd backend/services/game_engine
      PYTHONPATH=../.. uvicorn main:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: betbet-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: betbet-redis
          property: connectionString
      - key: MONGODB_URL
        sync: false

  # ──────────────────────────────
  # Betting Market (FastAPI)
  # ──────────────────────────────
  - type: web
    name: betbet-betting-market
    env: python
    plan: free
    region: oregon
    buildCommand: |
      cd backend
      pip install -r requirements.txt
    startCommand: |
      cd backend/services/betting_market
      PYTHONPATH=../.. uvicorn main:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: betbet-postgres
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: betbet-redis
          property: connectionString
      - key: MONGODB_URL
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false

# ──────────────────────────────
# PostgreSQL (managed)
# ──────────────────────────────
databases:
  - name: betbet-postgres
    plan: free
    region: oregon
    databaseName: betbet
    user: betbet_user
    ipAllowList: []     # keep it private