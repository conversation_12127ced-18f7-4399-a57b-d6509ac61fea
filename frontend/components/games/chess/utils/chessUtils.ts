import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Chess<PERSON>ove } from '../types';

export const parseSquare = (square: string): { file: number; rank: number } => {
  const file = square.charCodeAt(0) - 97; // 'a' = 0, 'b' = 1, etc.
  const rank = parseInt(square[1]) - 1; // '1' = 0, '2' = 1, etc.
  return { file, rank };
};

export const squareToIndex = (square: ChessSquare): number => {
  const { file, rank } = parseSquare(square);
  return rank * 8 + file;
};

export const indexToSquare = (index: number): ChessSquare => {
  const rank = Math.floor(index / 8);
  const file = index % 8;
  return `${String.fromCharCode(97 + file)}${rank + 1}` as ChessSquare;
};

export const isValidSquare = (square: string): square is ChessSquare => {
  return /^[a-h][1-8]$/.test(square);
};

export const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export const formatGameResult = (result: string): string => {
  switch (result) {
    case '1-0':
      return 'White wins';
    case '0-1':
      return 'Black wins';
    case '1/2-1/2':
      return 'Draw';
    default:
      return result;
  }
};

export const getTimeControlDisplay = (timeControl: string, customTimeLimit?: number, customIncrement?: number): string => {
  switch (timeControl) {
    case 'Bullet':
      return '1+0 Bullet';
    case 'Blitz':
      return '3+0 Blitz';
    case 'Rapid':
      return '10+0 Rapid';
    case 'Classical':
      return '30+0 Classical';
    case 'Custom':
      return `${customTimeLimit || 0}+${customIncrement || 0} Custom`;
    default:
      return timeControl;
  }
};

export const calculateTimeControlSeconds = (timeControl: string, customTimeLimit?: number): number => {
  switch (timeControl) {
    case 'Bullet':
      return 60; // 1 minute
    case 'Blitz':
      return 180; // 3 minutes
    case 'Rapid':
      return 600; // 10 minutes
    case 'Classical':
      return 1800; // 30 minutes
    case 'Custom':
      return (customTimeLimit || 0) * 60;
    default:
      return 600; // Default to 10 minutes
  }
};

export const calculateTimeIncrement = (timeControl: string, customIncrement?: number): number => {
  switch (timeControl) {
    case 'Bullet':
    case 'Blitz':
    case 'Rapid':
    case 'Classical':
      return 0; // No increment for standard time controls
    case 'Custom':
      return customIncrement || 0;
    default:
      return 0;
  }
};

export const isWhitePiece = (piece: ChessPiece): boolean => {
  return piece !== null && piece === piece.toUpperCase();
};

export const isBlackPiece = (piece: ChessPiece): boolean => {
  return piece !== null && piece === piece.toLowerCase();
};

export const getPieceColor = (piece: ChessPiece): 'white' | 'black' | null => {
  if (piece === null) return null;
  return isWhitePiece(piece) ? 'white' : 'black';
};

export const getPieceType = (piece: ChessPiece): string => {
  if (!piece) return '';

  const pieceMap: { [key: string]: string } = {
    'p': 'pawn', 'P': 'pawn',
    'n': 'knight', 'N': 'knight',
    'b': 'bishop', 'B': 'bishop',
    'r': 'rook', 'R': 'rook',
    'q': 'queen', 'Q': 'queen',
    'k': 'king', 'K': 'king'
  };

  return pieceMap[piece] || '';
};

export const validateMove = (from: ChessSquare, to: ChessSquare, legalMoves: string[]): boolean => {
  const moveUci = `${from}${to}`;
  return legalMoves.some(move => move.startsWith(moveUci));
};

export const parsePGN = (pgn: string): ChessMove[] => {
  // Simple PGN parsing - would need more sophisticated parsing for full PGN support
  const moves: ChessMove[] = [];
  const moveRegex = /\d+\.\s*([^\s]+)(?:\s+([^\s]+))?/g;
  let match;
  let moveNumber = 1;

  while ((match = moveRegex.exec(pgn)) !== null) {
    if (match[1]) {
      moves.push({
        moveUci: '', // Would need to convert from algebraic notation
        playerId: '', // Would need game context
        timestamp: new Date().toISOString(),
        moveNumber: moveNumber,
        fenAfter: '',
        timeUsed: 0
      });
    }
    if (match[2]) {
      moves.push({
        moveUci: '',
        playerId: '',
        timestamp: new Date().toISOString(),
        moveNumber: moveNumber,
        fenAfter: '',
        timeUsed: 0
      });
    }
    moveNumber++;
  }

  return moves;
};

export const generateShareableGameLink = (sessionId: string): string => {
  return `${window.location.origin}/chess/game/${sessionId}`;
};

export const copyToClipboard = (text: string): Promise<boolean> => {
  return navigator.clipboard.writeText(text)
    .then(() => true)
    .catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return true;
      } catch {
        document.body.removeChild(textArea);
        return false;
      }
    });
};