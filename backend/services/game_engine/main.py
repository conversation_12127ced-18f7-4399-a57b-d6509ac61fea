"""
BetBet Game Engine Service
Handles game sessions, tournaments, spectators, and real-time gaming
"""

import os
import uuid
import json
import asyncio
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from decimal import Decimal

from fastapi import FastAPI, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func
from sqlalchemy.orm import selectinload

import sys
sys.path.append('../..')
from shared.database import (
    init_all_databases, close_all_databases, get_db,
    get_mongo_db, get_redis_client
)
from shared.clerk_auth_simple import (
    ClerkUser, get_current_user, get_current_user_optional,
    verify_websocket_token, require_kyc_level
)
from shared.models import (
    Game, GameSession, GamePlayer, UserProfile,
    GameSessionCreate, GameSessionResponse
)
from pydantic import BaseModel
from tournament_models import Tournament, TournamentParticipant, BracketMatch, TournamentStatus
from tournament_manager import TournamentManager
from socketio_server import sio
import socketio


class ChessGameCreate(BaseModel):
    # Basic Game Settings
    timeControl: str  # 'Bullet', 'Blitz', 'Rapid', 'Classical', 'Custom'
    customTimeLimit: Optional[int] = None
    customIncrement: Optional[int] = None
    wagerAmount: float
    isRated: bool = True
    playerColor: str = "random"  # 'white', 'black', 'random'

    # Player & Privacy Settings
    inviteType: str = "open"  # 'open', 'specific', 'friends'
    specificPlayerId: Optional[str] = None
    isPrivate: bool = False
    requirePassword: bool = False
    gamePassword: Optional[str] = None

    # Advanced Settings
    variant: str = "standard"  # 'standard', 'chess960', 'king-of-the-hill', 'three-check'
    allowSpectators: bool = True
    allowChat: bool = True
    autoStart: bool = True

    # Tournament Settings
    isTournament: bool = False
    tournamentRounds: Optional[int] = None
from chess_engine import chess_manager, ChessTimeControl
from wagering import wagering_manager


# WebSocket connection manager
class GameWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.user_sessions: Dict[str, str] = {}  # user_id -> session_id

    async def connect(self, websocket: WebSocket, session_id: str, user_id: str):
        await websocket.accept()

        if session_id not in self.active_connections:
            self.active_connections[session_id] = []

        self.active_connections[session_id].append(websocket)
        self.user_sessions[user_id] = session_id

    def disconnect(self, websocket: WebSocket, session_id: str, user_id: str):
        if session_id in self.active_connections:
            if websocket in self.active_connections[session_id]:
                self.active_connections[session_id].remove(websocket)

            if not self.active_connections[session_id]:
                del self.active_connections[session_id]

        if user_id in self.user_sessions:
            del self.user_sessions[user_id]

    async def broadcast_to_session(self, session_id: str, message: dict, exclude_user: str = None):
        if session_id not in self.active_connections:
            return

        disconnected = []
        for connection in self.active_connections[session_id]:
            try:
                # Skip excluded user if specified
                if exclude_user and connection in [
                    conn for user_id, conn_session in self.user_sessions.items()
                    if conn_session == session_id and user_id == exclude_user
                ]:
                    continue

                await connection.send_json(message)
            except:
                disconnected.append(connection)

        # Clean up disconnected clients
        for conn in disconnected:
            if conn in self.active_connections[session_id]:
                self.active_connections[session_id].remove(conn)


manager = GameWebSocketManager()


# Lifespan manager for startup/shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting Game Engine Service...")
    await init_all_databases()
    yield
    # Shutdown
    print("Shutting down Game Engine Service...")
    await close_all_databases()


# Create FastAPI app
app = FastAPI(
    title="BetBet Game Engine Service",
    version="1.0.0",
    lifespan=lifespan
)

# Create Socket.IO ASGI app
socket_app = socketio.ASGIApp(sio, app)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# =============================================================================
# Game Catalog Endpoints
# =============================================================================

@app.get("/api/v1/games")
async def list_games(
    category: Optional[str] = None,
    page: int = 1,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """List available games - maps to Game Discovery Dashboard"""
    try:
        offset = (page - 1) * limit

        query = select(Game).where(Game.is_active == True)

        if category:
            query = query.where(Game.category == category)

        query = query.offset(offset).limit(limit).order_by(Game.name)

        result = await db.execute(query)
        games = result.scalars().all()

        # Get total count
        count_query = select(func.count(Game.id)).where(Game.is_active == True)
        if category:
            count_query = count_query.where(Game.category == category)

        total_result = await db.execute(count_query)
        total = total_result.scalar()

        return {
            "games": [
                {
                    "id": str(game.id),
                    "name": game.name,
                    "category": game.category,
                    "subcategory": game.subcategory,
                    "game_type": game.game_type,
                    "min_players": game.min_players,
                    "max_players": game.max_players,
                    "thumbnail_url": game.thumbnail_url,
                }
                for game in games
            ],
            "total": total,
            "page": page,
            "limit": limit,
            "has_next": offset + limit < total
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list games: {str(e)}")


@app.get("/api/v1/games/{game_id}")
async def get_game_details(game_id: str, db: AsyncSession = Depends(get_db)):
    """Get game details - maps to Game Cards Display"""
    try:
        game_uuid = uuid.UUID(game_id)
        result = await db.execute(
            select(Game).where(Game.id == game_uuid)
        )
        game = result.scalar_one_or_none()

        if not game:
            raise HTTPException(status_code=404, detail="Game not found")

        # Get active sessions count
        sessions_result = await db.execute(
            select(func.count(GameSession.id)).where(
                and_(
                    GameSession.game_id == game_uuid,
                    GameSession.status.in_(['waiting', 'active'])
                )
            )
        )
        active_sessions = sessions_result.scalar()

        return {
            "id": str(game.id),
            "name": game.name,
            "category": game.category,
            "subcategory": game.subcategory,
            "game_type": game.game_type,
            "min_players": game.min_players,
            "max_players": game.max_players,
            "rules": game.rules,
            "thumbnail_url": game.thumbnail_url,
            "active_sessions": active_sessions
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid game ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get game details: {str(e)}")


@app.get("/api/v1/games/categories")
async def get_game_categories(db: AsyncSession = Depends(get_db)):
    """Get game categories - maps to Category Navigation Bar"""
    try:
        result = await db.execute(
            select(Game.category, func.count(Game.id).label('count'))
            .where(Game.is_active == True)
            .group_by(Game.category)
            .order_by(Game.category)
        )

        categories = [
            {
                "name": row.category,
                "count": row.count
            }
            for row in result
        ]

        return {"categories": categories}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get categories: {str(e)}")


# =============================================================================
# Game Session Management
# =============================================================================

@app.post("/api/v1/sessions/create", response_model=GameSessionResponse)
async def create_game_session(
    session_data: GameSessionCreate,
    current_user: ClerkUser = Depends(require_kyc_level(1)),
    db: AsyncSession = Depends(get_db)
):
    """Create game session - maps to Match Creation Interface"""
    try:
        # Verify game exists
        game_uuid = uuid.UUID(session_data.game_id)
        result = await db.execute(
            select(Game).where(Game.id == game_uuid)
        )
        game = result.scalar_one_or_none()

        if not game or not game.is_active:
            raise HTTPException(status_code=404, detail="Game not found")

        # Get user profile
        profile_result = await db.execute(
            select(UserProfile).where(UserProfile.clerk_user_id == current_user.id)
        )
        profile = profile_result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="User profile not found")

        # Generate unique session code
        session_code = await generate_session_code(db)

        # Create game session
        session = GameSession(
            game_id=game_uuid,
            session_code=session_code,
            stake_amount=session_data.stake_amount,
            currency=session_data.currency,
            max_players=min(session_data.max_players, game.max_players),
            is_private=session_data.is_private,
            spectator_fee=session_data.spectator_fee,
            created_by_clerk_id=current_user.id,
            created_by_profile_id=profile.id
        )

        db.add(session)
        await db.commit()
        await db.refresh(session)

        # Auto-join creator as player
        player = GamePlayer(
            session_id=session.id,
            clerk_user_id=current_user.id,
            user_profile_id=profile.id,
            position=1
        )
        db.add(player)

        # Update session player count
        session.player_count = 1
        await db.commit()

        # Store game state in MongoDB
        mongo_db = get_mongo_db()
        await mongo_db.game_states.insert_one({
            "_id": str(session.id),
            "game_id": str(game_uuid),
            "current_state": {
                "status": "waiting",
                "players": [str(profile.id)],
                "turn": None,
                "timer": None,
                "board": [],
                "game_specific_data": {}
            },
            "events": [],
            "chat_messages": [],
            "created_at": datetime.utcnow(),
            "last_updated": datetime.utcnow()
        })

        return session

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid game ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@app.post("/api/v1/sessions/{session_id}/join")
async def join_game_session(
    session_id: str,
    current_user: ClerkUser = Depends(require_kyc_level(1)),
    db: AsyncSession = Depends(get_db)
):
    """Join game session - maps to Quick Join Button"""
    try:
        session_uuid = uuid.UUID(session_id)

        # Get session
        result = await db.execute(
            select(GameSession)
            .options(selectinload(GameSession.game))
            .where(GameSession.id == session_uuid)
        )
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.status not in ['waiting']:
            raise HTTPException(status_code=400, detail="Session is not accepting players")

        if session.player_count >= session.max_players:
            raise HTTPException(status_code=400, detail="Session is full")

        # Check if user already in session
        existing_result = await db.execute(
            select(GamePlayer).where(
                and_(
                    GamePlayer.session_id == session_uuid,
                    GamePlayer.clerk_user_id == current_user.id
                )
            )
        )
        if existing_result.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="Already in this session")

        # Get user profile
        profile_result = await db.execute(
            select(UserProfile).where(UserProfile.clerk_user_id == current_user.id)
        )
        profile = profile_result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="User profile not found")

        # Add player
        player = GamePlayer(
            session_id=session_uuid,
            clerk_user_id=current_user.id,
            user_profile_id=profile.id,
            position=session.player_count + 1
        )
        db.add(player)

        # Update session
        session.player_count += 1

        # Auto-start if at minimum players
        if session.player_count >= session.game.min_players and session.status == 'waiting':
            session.status = 'active'
            session.started_at = datetime.utcnow()

        await db.commit()

        # Update game state in MongoDB
        mongo_db = get_mongo_db()
        await mongo_db.game_states.update_one(
            {"_id": session_id},
            {
                "$push": {"current_state.players": str(profile.id)},
                "$set": {
                    "current_state.status": session.status,
                    "last_updated": datetime.utcnow()
                }
            }
        )

        # Broadcast to session
        await manager.broadcast_to_session(
            session_id,
            {
                "type": "player_joined",
                "user_id": current_user.id,
                "username": profile.username,
                "position": player.position,
                "player_count": session.player_count,
                "session_status": session.status
            }
        )

        return {
            "status": "success",
            "session_status": session.status,
            "position": player.position,
            "player_count": session.player_count
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid session ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to join session: {str(e)}")


@app.get("/api/v1/sessions/active")
async def get_active_sessions(
    game_id: Optional[str] = None,
    stake_min: Optional[float] = None,
    stake_max: Optional[float] = None,
    page: int = 1,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """Get active sessions - maps to Game Browser"""
    try:
        offset = (page - 1) * limit

        query = (
            select(GameSession)
            .options(selectinload(GameSession.game), selectinload(GameSession.creator))
            .where(GameSession.status.in_(['waiting', 'active']))
            .where(GameSession.is_private == False)
        )

        if game_id:
            query = query.where(GameSession.game_id == uuid.UUID(game_id))

        if stake_min:
            query = query.where(GameSession.stake_amount >= stake_min)

        if stake_max:
            query = query.where(GameSession.stake_amount <= stake_max)

        query = query.offset(offset).limit(limit).order_by(GameSession.created_at.desc())

        result = await db.execute(query)
        sessions = result.scalars().all()

        return {
            "sessions": [
                {
                    "id": str(session.id),
                    "session_code": session.session_code,
                    "game": {
                        "id": str(session.game.id),
                        "name": session.game.name,
                        "category": session.game.category,
                        "thumbnail_url": session.game.thumbnail_url
                    },
                    "creator": {
                        "username": session.creator.username,
                        "display_name": session.creator.display_name
                    },
                    "stake_amount": float(session.stake_amount),
                    "currency": session.currency,
                    "player_count": session.player_count,
                    "max_players": session.max_players,
                    "status": session.status,
                    "spectator_fee": float(session.spectator_fee),
                    "created_at": session.created_at
                }
                for session in sessions
            ],
            "page": page,
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get active sessions: {str(e)}")


# =============================================================================
# Real-time Gaming WebSocket
# =============================================================================

@app.websocket("/ws/spectate/{session_id}")
async def spectator_websocket(websocket: WebSocket, session_id: str, token: str):
    """WebSocket for spectators to watch games"""
    try:
        # Verify token
        user = await verify_websocket_token(token)

        # Connect to WebSocket (spectators don't need to be players)
        await manager.connect(websocket, session_id, f"spectator_{user.id}")

        # Send initial game state
        chess_game = chess_manager.get_game(session_id)
        if chess_game:
            await websocket.send_json({
                "type": "game_state",
                "state": chess_game.get_game_state(),
                "spectator_mode": True
            })

        # Handle spectator messages (betting, chat)
        while True:
            data = await websocket.receive_json()

            if data['type'] == 'spectator_bet':
                bet_result = await wagering_manager.handle_spectator_bet(
                    session_id=session_id,
                    spectator_id=user.id,
                    amount=Decimal(str(data['amount'])),
                    prediction=data['prediction'],
                    currency=data.get('currency', 'USD')
                )
                await websocket.send_json({
                    "type": "bet_result",
                    "result": bet_result
                })

            elif data['type'] == 'chat_message':
                await handle_chat_message(session_id, f"spectator_{user.id}", data)

            elif data['type'] == 'ping':
                await websocket.send_json({"type": "pong"})

    except WebSocketDisconnect:
        manager.disconnect(websocket, session_id, f"spectator_{user.id}")
    except Exception as e:
        print(f"Spectator WebSocket error: {e}")
        await websocket.close(code=1011, reason="Internal server error")


@app.websocket("/ws/game/{session_id}")
async def game_websocket(websocket: WebSocket, session_id: str, token: str):
    """WebSocket for real-time game updates"""
    try:
        # Verify token
        user = await verify_websocket_token(token)

        # Verify user is in session
        db_gen = get_db()
        db = await db_gen.__anext__()

        try:
            result = await db.execute(
                select(GamePlayer).where(
                    and_(
                        GamePlayer.session_id == uuid.UUID(session_id),
                        GamePlayer.clerk_user_id == user.id
                    )
                )
            )
            player = result.scalar_one_or_none()

            if not player:
                await websocket.close(code=1008, reason="Not a player in this session")
                return

        finally:
            await db.close()

        # Connect to WebSocket
        await manager.connect(websocket, session_id, user.id)

        # Send initial state
        mongo_db = get_mongo_db()
        game_state = await mongo_db.game_states.find_one({"_id": session_id})

        if game_state:
            await websocket.send_json({
                "type": "game_state",
                "state": game_state["current_state"]
            })

        # Handle messages
        while True:
            data = await websocket.receive_json()

            if data['type'] in ['game_move', 'chess_move', 'chess_resign', 'chess_draw_offer', 'chess_draw_accept']:
                await handle_game_move(session_id, user.id, data)
            elif data['type'] == 'chat_message':
                await handle_chat_message(session_id, user.id, data)
            elif data['type'] == 'ping':
                await websocket.send_json({"type": "pong"})

    except WebSocketDisconnect:
        manager.disconnect(websocket, session_id, user.id)
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.close(code=1011, reason="Internal server error")


async def handle_game_move(session_id: str, user_id: str, move_data: dict):
    """Handle game move with chess engine integration"""
    try:
        mongo_db = get_mongo_db()
        redis_client = get_redis_client()

        # Get chess game
        chess_game = chess_manager.get_game(session_id)
        if not chess_game:
            # Game not in memory, try to load from MongoDB
            game_state = await mongo_db.game_states.find_one({"_id": session_id})
            if not game_state or game_state.get("game_type") != "chess":
                return

        # Handle chess move
        if move_data['type'] == 'chess_move':
            move_uci = move_data['data']['move']
            result = chess_game.make_move(user_id, move_uci)

            if 'error' in result:
                # Send error back to player
                await manager.broadcast_to_session(
                    session_id,
                    {
                        "type": "move_error",
                        "error": result['error'],
                        "user_id": user_id
                    }
                )
                return

            # Update game state in MongoDB
            await mongo_db.game_states.update_one(
                {"_id": session_id},
                {
                    "$set": {
                        "current_state": chess_game.get_game_state(),
                        "last_updated": datetime.utcnow()
                    },
                    "$push": {
                        "events": {
                            "type": "chess_move",
                            "user_id": user_id,
                            "move_uci": move_uci,
                            "timestamp": datetime.utcnow()
                        }
                    }
                }
            )

            # Broadcast game update
            await manager.broadcast_to_session(
                session_id,
                {
                    "type": "chess_update",
                    "game_state": result,
                    "move": move_uci,
                    "player": user_id
                }
            )

            # Handle game end
            if chess_game.game_ended:
                await handle_game_end(session_id, chess_game)

        elif move_data['type'] == 'chess_resign':
            result = chess_game.resign(user_id)
            await handle_game_end(session_id, chess_game)

            await manager.broadcast_to_session(
                session_id,
                {
                    "type": "chess_game_ended",
                    "reason": "resignation",
                    "winner": result.get('winner'),
                    "game_result": result.get('game_result')
                }
            )

        elif move_data['type'] == 'chess_draw_offer':
            await manager.broadcast_to_session(
                session_id,
                {
                    "type": "chess_draw_offer",
                    "offered_by": user_id
                }
            )

        elif move_data['type'] == 'chess_draw_accept':
            result = chess_game.accept_draw()
            await handle_game_end(session_id, chess_game)

            await manager.broadcast_to_session(
                session_id,
                {
                    "type": "chess_game_ended",
                    "reason": "draw_agreement",
                    "game_result": "1/2-1/2"
                }
            )

        # Publish to Redis for other services (wallet, analytics)
        await redis_client.publish(
            f"game:{session_id}",
            json.dumps({
                "type": "chess_event",
                "session_id": session_id,
                "user_id": user_id,
                "event_data": move_data,
                "game_ended": chess_game.game_ended if chess_game else False
            })
        )

    except Exception as e:
        print(f"Error handling chess move: {e}")


async def handle_chat_message(session_id: str, user_id: str, message_data: dict):
    """Handle chat message"""
    try:
        mongo_db = get_mongo_db()

        # Add to chat history
        await mongo_db.game_states.update_one(
            {"_id": session_id},
            {
                "$push": {
                    "chat_messages": {
                        "user_id": user_id,
                        "message": message_data['message'],
                        "timestamp": datetime.utcnow()
                    }
                }
            }
        )

        # Broadcast message
        await manager.broadcast_to_session(
            session_id,
            {
                "type": "chat_message",
                "user_id": user_id,
                "message": message_data['message'],
                "timestamp": datetime.utcnow().isoformat()
            }
        )

    except Exception as e:
        print(f"Error handling chat message: {e}")


# =============================================================================
# Chess Game Endpoints
# =============================================================================

@app.post("/api/v1/chess/create")
async def create_chess_game(
    game_config: ChessGameCreate,
    current_user: ClerkUser = Depends(require_kyc_level(1)),
    db: AsyncSession = Depends(get_db)
):
    """Create a new chess game with full frontend configuration support"""
    try:
        # Map time control to minutes and increment
        time_mapping = {
            "Bullet": (1, 1),
            "Blitz": (3, 2),
            "Rapid": (10, 10),
            "Classical": (30, 30),
            "Custom": (game_config.customTimeLimit or 10, game_config.customIncrement or 0)
        }

        time_minutes, increment_seconds = time_mapping.get(game_config.timeControl, (10, 0))

        # Validate custom time controls
        if game_config.timeControl == "Custom":
            if not game_config.customTimeLimit or game_config.customTimeLimit < 1:
                raise HTTPException(status_code=400, detail="Custom time limit must be at least 1 minute")
            if game_config.customIncrement and game_config.customIncrement < 0:
                raise HTTPException(status_code=400, detail="Custom increment cannot be negative")

        # Validate wager amount
        if game_config.wagerAmount < 0:
            raise HTTPException(status_code=400, detail="Wager amount cannot be negative")

        # Generate session ID
        session_id = str(uuid.uuid4())

        # Create time control
        time_control = ChessTimeControl(time_minutes, increment_seconds)

        # Determine players based on inviteType
        white_player_id = current_user.id
        black_player_id = None

        if game_config.inviteType == "specific" and game_config.specificPlayerId:
            if game_config.playerColor == "white":
                white_player_id = current_user.id
                black_player_id = game_config.specificPlayerId
            elif game_config.playerColor == "black":
                white_player_id = game_config.specificPlayerId
                black_player_id = current_user.id
            else:  # random
                import random
                if random.choice([True, False]):
                    white_player_id = current_user.id
                    black_player_id = game_config.specificPlayerId
                else:
                    white_player_id = game_config.specificPlayerId
                    black_player_id = current_user.id

        # For open games or friends games, only set creator for now
        elif game_config.inviteType in ["open", "friends"]:
            if game_config.playerColor == "white":
                white_player_id = current_user.id
                black_player_id = "WAITING"
            elif game_config.playerColor == "black":
                white_player_id = "WAITING"
                black_player_id = current_user.id
            else:  # random - will be determined when second player joins
                white_player_id = current_user.id
                black_player_id = "WAITING"

        # Create chess game
        chess_game = chess_manager.create_game(
            session_id=session_id,
            white_player_id=white_player_id,
            black_player_id=black_player_id if black_player_id != "WAITING" else None,
            time_control=time_control,
            stake_amount=game_config.wagerAmount,
            currency="USD"
        )

        # Store comprehensive game configuration in MongoDB
        mongo_db = get_mongo_db()
        await mongo_db.game_states.insert_one({
            "_id": session_id,
            "game_type": "chess",
            "game_config": {
                "timeControl": game_config.timeControl,
                "customTimeLimit": game_config.customTimeLimit,
                "customIncrement": game_config.customIncrement,
                "wagerAmount": game_config.wagerAmount,
                "isRated": game_config.isRated,
                "playerColor": game_config.playerColor,
                "inviteType": game_config.inviteType,
                "specificPlayerId": game_config.specificPlayerId,
                "isPrivate": game_config.isPrivate,
                "requirePassword": game_config.requirePassword,
                "gamePassword": game_config.gamePassword,
                "variant": game_config.variant,
                "allowSpectators": game_config.allowSpectators,
                "allowChat": game_config.allowChat,
                "autoStart": game_config.autoStart,
                "isTournament": game_config.isTournament,
                "tournamentRounds": game_config.tournamentRounds
            },
            "current_state": chess_game.get_game_state() if black_player_id != "WAITING" else {
                "status": "waiting_for_player",
                "creator_id": current_user.id,
                "white_player": white_player_id if white_player_id != "WAITING" else None,
                "black_player": black_player_id if black_player_id != "WAITING" else None,
                "waiting_for": "black" if white_player_id == current_user.id else "white"
            },
            "events": [],
            "chat_messages": [],
            "created_at": datetime.utcnow(),
            "last_updated": datetime.utcnow()
        })

        # Start the game if both players are assigned
        if black_player_id and black_player_id != "WAITING":
            game_state = chess_game.start_game()
            game_status = "ready_to_start" if not game_config.autoStart else "started"
        else:
            game_state = {
                "status": "waiting_for_player",
                "session_id": session_id,
                "creator": current_user.id,
                "invite_type": game_config.inviteType,
                "is_private": game_config.isPrivate,
                "require_password": game_config.requirePassword
            }
            game_status = "waiting_for_player"

        return {
            "session_id": session_id,
            "status": game_status,
            "game_config": game_config.dict(),
            "game_state": game_state,
            "websocket_url": f"/ws/game/{session_id}",
            "spectator_websocket_url": f"/ws/spectate/{session_id}" if game_config.allowSpectators else None,
            "join_url": f"/chess/join/{session_id}" if game_config.inviteType == "open" else None
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create chess game: {str(e)}")


@app.get("/api/v1/chess/{session_id}/state")
async def get_chess_game_state(session_id: str):
    """Get current chess game state"""
    try:
        chess_game = chess_manager.get_game(session_id)
        if not chess_game:
            raise HTTPException(status_code=404, detail="Chess game not found")

        return chess_game.get_game_state()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get game state: {str(e)}")


@app.post("/api/v1/chess/{session_id}/join")
async def join_chess_game(
    session_id: str,
    password: Optional[str] = None,
    current_user: ClerkUser = Depends(require_kyc_level(1)),
    db: AsyncSession = Depends(get_db)
):
    """Join an open chess game"""
    try:
        # Get game configuration from MongoDB
        mongo_db = get_mongo_db()
        game_doc = await mongo_db.game_states.find_one({"_id": session_id})

        if not game_doc:
            raise HTTPException(status_code=404, detail="Game not found")

        game_config = game_doc.get("game_config", {})
        current_state = game_doc.get("current_state", {})

        # Check if game is joinable
        if current_state.get("status") != "waiting_for_player":
            raise HTTPException(status_code=400, detail="Game is not accepting players")

        # Check password if required
        if game_config.get("requirePassword") and password != game_config.get("gamePassword"):
            raise HTTPException(status_code=403, detail="Invalid game password")

        # Check if user is already in game
        if current_state.get("creator_id") == current_user.id:
            raise HTTPException(status_code=400, detail="You cannot join your own game")

        # Place stake if required
        stake_amount = game_config.get("wagerAmount", 0)
        if stake_amount > 0:
            stake_result = await wagering_manager.place_stake(
                session_id=session_id,
                player_id=current_user.id,
                amount=Decimal(str(stake_amount)),
                currency="USD"
            )
            if "error" in stake_result:
                raise HTTPException(status_code=400, detail=stake_result["error"])

        # Determine player colors
        creator_id = current_state.get("creator_id")
        waiting_for = current_state.get("waiting_for", "black")

        if waiting_for == "black":
            white_player_id = creator_id
            black_player_id = current_user.id
        else:
            white_player_id = current_user.id
            black_player_id = creator_id

        # Handle random color assignment if specified
        if game_config.get("playerColor") == "random":
            import random
            if random.choice([True, False]):
                white_player_id, black_player_id = black_player_id, white_player_id

        # Create time control
        time_minutes = 10  # Default
        increment_seconds = 0

        time_mapping = {
            "Bullet": (1, 1),
            "Blitz": (3, 2),
            "Rapid": (10, 10),
            "Classical": (30, 30),
            "Custom": (game_config.get("customTimeLimit", 10), game_config.get("customIncrement", 0))
        }

        time_control_name = game_config.get("timeControl", "Rapid")
        time_minutes, increment_seconds = time_mapping.get(time_control_name, (10, 0))

        time_control = ChessTimeControl(time_minutes, increment_seconds)

        # Create and start the chess game
        chess_game = chess_manager.create_game(
            session_id=session_id,
            white_player_id=white_player_id,
            black_player_id=black_player_id,
            time_control=time_control,
            stake_amount=stake_amount,
            currency="USD"
        )

        # Start the game if autoStart is enabled
        if game_config.get("autoStart", True):
            game_state = chess_game.start_game()
            status = "started"
        else:
            game_state = chess_game.get_game_state()
            status = "ready_to_start"

        # Update game state in MongoDB
        await mongo_db.game_states.update_one(
            {"_id": session_id},
            {
                "$set": {
                    "current_state": game_state,
                    "last_updated": datetime.utcnow()
                }
            }
        )

        # Broadcast to existing connections
        await manager.broadcast_to_session(
            session_id,
            {
                "type": "player_joined",
                "white_player": white_player_id,
                "black_player": black_player_id,
                "status": status,
                "game_state": game_state
            }
        )

        return {
            "status": "success",
            "game_status": status,
            "your_color": "white" if current_user.id == white_player_id else "black",
            "opponent_id": black_player_id if current_user.id == white_player_id else white_player_id,
            "game_state": game_state,
            "websocket_url": f"/ws/game/{session_id}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to join game: {str(e)}")


@app.post("/api/v1/chess/{session_id}/spectator-bet")
async def place_spectator_bet(
    session_id: str,
    bet_amount: float,
    prediction: str,  # "white_wins", "black_wins", "draw"
    current_user: ClerkUser = Depends(require_kyc_level(1))
):
    """Place a spectator bet on chess game outcome"""
    try:
        chess_game = chess_manager.get_game(session_id)
        if not chess_game:
            raise HTTPException(status_code=404, detail="Chess game not found")

        if prediction not in ["white_wins", "black_wins", "draw"]:
            raise HTTPException(status_code=400, detail="Invalid prediction")

        result = chess_game.place_spectator_bet(current_user.id, bet_amount, prediction)

        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])

        # TODO: Integrate with wallet service to deduct bet amount

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to place bet: {str(e)}")


# =============================================================================
# Spectator System
# =============================================================================

@app.post("/api/v1/sessions/{session_id}/spectate")
async def join_as_spectator(
    session_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Join as spectator - maps to Watch Interface"""
    try:
        session_uuid = uuid.UUID(session_id)

        # Get session
        result = await db.execute(
            select(GameSession).where(GameSession.id == session_uuid)
        )
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.status not in ['active']:
            raise HTTPException(status_code=400, detail="Session not active for spectating")

        # Handle spectator fee if required
        if session.spectator_fee > 0:
            # TODO: Implement payment processing
            pass

        return {
            "status": "success",
            "spectator_fee": float(session.spectator_fee),
            "websocket_url": f"/ws/spectate/{session_id}"
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid session ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to join as spectator: {str(e)}")


# =============================================================================
# Utility Functions
# =============================================================================

async def generate_session_code(db: AsyncSession) -> str:
    """Generate unique session code"""
    import random
    import string

    while True:
        code = "".join(random.choices(string.ascii_uppercase + string.digits, k=8))

        result = await db.execute(
            select(GameSession).where(GameSession.session_code == code)
        )
        if not result.scalar_one_or_none():
            return code


async def handle_game_end(session_id: str, chess_game):
    """Handle chess game end and payouts"""
    try:
        redis_client = get_redis_client()

        # Publish game end event for wallet service to handle payouts
        await redis_client.publish(
            "game_payouts",
            json.dumps({
                "session_id": session_id,
                "game_type": "chess",
                "winner": chess_game.winner,
                "game_result": chess_game.game_result,
                "main_payout": chess_game._calculate_main_payout(),
                "spectator_payouts": chess_game._calculate_spectator_payouts(),
                "stake_amount": chess_game.stake_amount,
                "currency": chess_game.currency
            })
        )

        # Clean up game from memory after some time
        # In production, you might want to keep it longer for replays
        await asyncio.sleep(300)  # Keep for 5 minutes
        chess_manager.remove_game(session_id)

    except Exception as e:
        print(f"Error handling game end: {e}")


# =============================================================================
# Tournament Models
# =============================================================================

class TournamentCreate(BaseModel):
    name: str
    description: Optional[str] = ""
    game_type: str = "chess"
    format: str = "single_elimination"  # single_elimination, double_elimination, round_robin, swiss
    max_participants: int
    entry_fee: float = 0.0
    currency: str = "USD"
    registration_end: str  # ISO datetime
    tournament_start: str  # ISO datetime
    game_settings: Optional[Dict[str, Any]] = {}
    is_rated: bool = True
    allow_spectators: bool = True
    allow_chat: bool = True
    organizer_name: Optional[str] = ""

class TournamentRegister(BaseModel):
    player_name: str
    rating: Optional[int] = None

class TournamentMatchComplete(BaseModel):
    winner_id: str
    result: str


# =============================================================================
# Tournament Endpoints
# =============================================================================

@app.post("/api/v1/tournaments/create")
async def create_tournament(
    tournament_data: TournamentCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new tournament"""
    try:
        tournament_manager = TournamentManager(db)

        result = await tournament_manager.create_tournament({
            **tournament_data.model_dump(),
            "creator_id": current_user.user_id
        })

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tournament: {str(e)}")


@app.get("/api/v1/tournaments")
async def list_tournaments(
    status: Optional[str] = None,
    game_type: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """List tournaments with optional filtering"""
    try:
        query = select(Tournament)

        if status:
            query = query.where(Tournament.status == TournamentStatus(status))
        if game_type:
            query = query.where(Tournament.game_type == game_type)

        query = query.order_by(Tournament.created_at.desc()).limit(50)

        result = await db.execute(query)
        tournaments = result.scalars().all()

        return {
            "tournaments": [
                {
                    "id": t.id,
                    "name": t.name,
                    "description": t.description,
                    "game_type": t.game_type,
                    "format": t.format.value,
                    "status": t.status.value,
                    "max_participants": t.max_participants,
                    "entry_fee": t.entry_fee,
                    "prize_pool": t.prize_pool,
                    "currency": t.currency,
                    "registration_end": t.registration_end.isoformat(),
                    "tournament_start": t.tournament_start.isoformat(),
                    "creator_id": t.creator_id,
                    "created_at": t.created_at.isoformat()
                } for t in tournaments
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list tournaments: {str(e)}")


@app.get("/api/v1/tournaments/{tournament_id}")
async def get_tournament(
    tournament_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get tournament details with participants and matches"""
    try:
        # Get tournament
        tournament_result = await db.execute(
            select(Tournament)
            .options(selectinload(Tournament.participants))
            .where(Tournament.id == tournament_id)
        )
        tournament = tournament_result.scalar_one_or_none()

        if not tournament:
            raise HTTPException(status_code=404, detail="Tournament not found")

        # Get matches
        matches_result = await db.execute(
            select(BracketMatch)
            .where(BracketMatch.tournament_id == tournament_id)
            .order_by(BracketMatch.round_number, BracketMatch.match_number)
        )
        matches = matches_result.scalars().all()

        return {
            "tournament": {
                "id": tournament.id,
                "name": tournament.name,
                "description": tournament.description,
                "game_type": tournament.game_type,
                "format": tournament.format.value,
                "status": tournament.status.value,
                "max_participants": tournament.max_participants,
                "entry_fee": tournament.entry_fee,
                "prize_pool": tournament.prize_pool,
                "currency": tournament.currency,
                "registration_end": tournament.registration_end.isoformat(),
                "tournament_start": tournament.tournament_start.isoformat(),
                "creator_id": tournament.creator_id,
                "created_at": tournament.created_at.isoformat()
            },
            "participants": [
                {
                    "id": p.id,
                    "player_id": p.player_id,
                    "player_name": p.player_name,
                    "seed_number": p.seed_number,
                    "rating": p.rating,
                    "is_active": p.is_active,
                    "final_position": p.final_position,
                    "prize_amount": p.prize_amount,
                    "registered_at": p.registered_at.isoformat()
                } for p in tournament.participants
            ],
            "matches": [
                {
                    "id": m.id,
                    "round_number": m.round_number,
                    "match_number": m.match_number,
                    "status": m.status.value,
                    "player1_id": m.player1_id,
                    "player1_name": m.player1_name,
                    "player1_seed": m.player1_seed,
                    "player2_id": m.player2_id,
                    "player2_name": m.player2_name,
                    "player2_seed": m.player2_seed,
                    "winner_id": m.winner_id,
                    "result": m.result,
                    "scheduled_start": m.scheduled_start.isoformat() if m.scheduled_start else None,
                    "completed_at": m.completed_at.isoformat() if m.completed_at else None
                } for m in matches
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tournament: {str(e)}")


@app.post("/api/v1/tournaments/{tournament_id}/register")
async def register_for_tournament(
    tournament_id: str,
    registration_data: TournamentRegister,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Register for a tournament"""
    try:
        tournament_manager = TournamentManager(db)

        result = await tournament_manager.register_participant(tournament_id, {
            "player_id": current_user.user_id,
            "player_name": registration_data.player_name,
            "rating": registration_data.rating
        })

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to register for tournament: {str(e)}")


@app.post("/api/v1/tournaments/{tournament_id}/start")
async def start_tournament(
    tournament_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Start a tournament (creator only)"""
    try:
        # Verify tournament creator
        tournament_result = await db.execute(
            select(Tournament).where(Tournament.id == tournament_id)
        )
        tournament = tournament_result.scalar_one_or_none()

        if not tournament:
            raise HTTPException(status_code=404, detail="Tournament not found")

        if tournament.creator_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="Only tournament creator can start tournament")

        tournament_manager = TournamentManager(db)
        result = await tournament_manager.start_tournament(tournament_id)

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start tournament: {str(e)}")


@app.post("/api/v1/tournaments/matches/{match_id}/complete")
async def complete_tournament_match(
    match_id: str,
    completion_data: TournamentMatchComplete,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Complete a tournament match"""
    try:
        # Verify match exists and user is participant
        match_result = await db.execute(
            select(BracketMatch).where(BracketMatch.id == match_id)
        )
        match = match_result.scalar_one_or_none()

        if not match:
            raise HTTPException(status_code=404, detail="Match not found")

        # For now, allow either participant to report result
        # In production, you'd want proper verification
        if current_user.user_id not in [match.player1_id, match.player2_id]:
            raise HTTPException(status_code=403, detail="Only match participants can report results")

        tournament_manager = TournamentManager(db)
        result = await tournament_manager.complete_match(
            match_id,
            completion_data.winner_id,
            completion_data.result
        )

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to complete match: {str(e)}")


# =============================================================================
# Health Check
# =============================================================================

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "game_engine"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:socket_app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )