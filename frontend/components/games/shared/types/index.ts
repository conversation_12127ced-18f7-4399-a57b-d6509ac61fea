// Shared Game Types

export interface BaseGameState {
  sessionId: string;
  gameStarted: boolean;
  gameEnded: boolean;
  winner?: string;
  gameResult?: string;
  stakeAmount: number;
  currency: string;
  spectatorPool: number;
  gameType: string;
  createdAt: string;
  startedAt?: string;
  endedAt?: string;
}

export interface BasePlayer {
  id: string;
  username: string;
  displayName?: string;
  avatarUrl?: string;
  rating?: number;
  level?: number;
  isOnline: boolean;
  isFriend?: boolean;
}

export interface BaseSpectatorBet {
  spectatorId: string;
  amount: number;
  prediction: string;
  timestamp: string;
  odds: Record<string, number>;
}

export interface BaseWebSocketMessage {
  type: string;
  data?: any;
}

export interface BaseGameResponse {
  sessionId: string;
  status: string;
  gameConfig: any;
  gameState: any;
  websocketUrl: string;
  spectatorWebsocketUrl?: string;
  joinUrl?: string;
}

export interface BaseJoinResponse {
  status: string;
  gameStatus: string;
  gameState: BaseGameState;
  websocketUrl: string;
}

// Common game enums
export enum GameStatus {
  WAITING_FOR_PLAYER = 'waiting_for_player',
  READY_TO_START = 'ready_to_start',
  STARTED = 'started',
  ENDED = 'ended',
  CANCELLED = 'cancelled'
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  BTC = 'BTC',
  ETH = 'ETH'
}