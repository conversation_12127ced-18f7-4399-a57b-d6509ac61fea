"""
Database seeder for chess games
"""

import asyncio
import sys
sys.path.append('../..')

from shared.database import init_all_databases, get_db
from shared.models import Game


async def seed_chess_games():
    """Seed the database with chess game variants"""

    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker

    # Use the database URL from the service
    DATABASE_URL = "postgresql+asyncpg://betbet:123Bubblegums@localhost:5432/betbet_main"

    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as db:
        try:
            chess_games = [
            {
                "name": "Chess - Bullet",
                "category": "Board Games",
                "subcategory": "Chess",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Fast-paced chess with less than 3 minutes per player",
                    "time_control": {"base_minutes": 1, "increment_seconds": 0},
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-bullet.png"
            },
            {
                "name": "Chess - Blitz",
                "category": "Board Games",
                "subcategory": "Chess",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Quick chess games between 3-10 minutes per player",
                    "time_control": {"base_minutes": 5, "increment_seconds": 0},
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-blitz.png"
            },
            {
                "name": "Chess - Rapid",
                "category": "Board Games",
                "subcategory": "Chess",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Medium-paced chess with 10-60 minutes per player",
                    "time_control": {"base_minutes": 15, "increment_seconds": 10},
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-rapid.png"
            },
            {
                "name": "Chess - Classical",
                "category": "Board Games",
                "subcategory": "Chess",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Traditional long-format chess with 60+ minutes per player",
                    "time_control": {"base_minutes": 90, "increment_seconds": 30},
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-classical.png"
            },
            {
                "name": "Chess - King of the Hill",
                "category": "Board Games",
                "subcategory": "Chess Variants",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Win by checkmate or by getting your king to the center",
                    "time_control": {"base_minutes": 10, "increment_seconds": 0},
                    "variant": "king_of_the_hill",
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-koth.png"
            },
            {
                "name": "Chess - Three-Check",
                "category": "Board Games",
                "subcategory": "Chess Variants",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Win by giving check three times or by checkmate",
                    "time_control": {"base_minutes": 5, "increment_seconds": 3},
                    "variant": "three_check",
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-3check.png"
            }
        ]

        # Add other board games for Phase 1
        other_games = [
            {
                "name": "Checkers",
                "category": "Board Games",
                "subcategory": "Classic",
                "game_type": "turn_based",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Classic checkers with jumping and king pieces",
                    "features": ["turn_based", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/checkers.png"
            },
            {
                "name": "Connect Four",
                "category": "Board Games",
                "subcategory": "Classic",
                "game_type": "turn_based",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Connect four pieces in a row to win",
                    "features": ["turn_based", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/connect-four.png"
            },
            {
                "name": "Poker - Texas Hold'em",
                "category": "Casino Games",
                "subcategory": "Poker",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 8,
                "rules": {
                    "description": "Classic Texas Hold'em poker with community cards",
                    "features": ["real_time", "multiplayer", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/poker-holdem.png"
            },
            {
                "name": "Blackjack",
                "category": "Casino Games",
                "subcategory": "Card Games",
                "game_type": "realtime",
                "min_players": 1,
                "max_players": 6,
                "rules": {
                    "description": "Beat the dealer by getting closest to 21 without going over",
                    "features": ["real_time", "multiplayer", "wagering"]
                },
                "thumbnail_url": "/images/blackjack.png"
            }
        ]

        all_games = chess_games + other_games

        for game_data in all_games:
            game = Game(**game_data)
            db.add(game)

        await db.commit()
        print(f"✅ Successfully seeded {len(all_games)} games to the database!")

        # Print game IDs for reference
        from sqlalchemy import select
        result = await db.execute(select(Game).where(Game.is_active == True))
        games = result.scalars().all()

        print("\n📋 Game IDs for reference:")
        for game in games:
            print(f"• {game.name}: {game.id}")

        except Exception as e:
            print(f"❌ Error seeding games: {e}")
            await db.rollback()


if __name__ == "__main__":
    asyncio.run(seed_chess_games())