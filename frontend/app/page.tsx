"use client"

import { useA<PERSON>, useUser } from "@clerk/nextjs"
import { SignInButton, SignUpButton } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowRight,
  Users,
  Gamepad2,
  BarChart3,
  Target,
  Shield,
  Star,
  Trophy,
  TrendingUp,
  Zap,
  DollarSign,
  Activity
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function HomePage() {
  const { isSignedIn, isLoaded } = useAuth()
  const router = useRouter()

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (isLoaded && isSignedIn) {
      router.push("/dashboard")
    }
  }, [isSignedIn, isLoaded, router])

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="relative h-12 w-12 mb-4 mx-auto animate-pulse">
            <Image
              src="/images/betbet.logo.png"
              alt="BetBet Logo"
              width={48}
              height={48}
              className="rounded-lg"
            />
          </div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section with Background */}
      <div className="relative min-h-screen">
        {/* Background Image - Full viewport */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wheel.avif"
            alt="Casino Roulette Background"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/60" />
        </div>

        {/* Header */}
        <header className="relative z-10 border-b border-white/10 bg-black/20 backdrop-blur-sm">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative h-10 w-10 rounded-2xl overflow-hidden">
                  <Image
                    src="/images/betbet.logo.png"
                    alt="BetBet Logo"
                    width={40}
                    height={40}
                    className="object-cover"
                  />
                </div>
                <span className="text-2xl font-bold text-white">BetBet</span>
              </div>

              <div className="flex items-center space-x-4">
                <SignInButton mode="modal">
                  <Button variant="ghost" className="text-white hover:text-white hover:bg-white/10">
                    Sign In
                  </Button>
                </SignInButton>
                <SignUpButton mode="modal">
                  <Button className="bg-pink-500 hover:bg-pink-600 text-white border-0">
                    Get Started
                  </Button>
                </SignUpButton>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Content */}
        <section className="relative z-10 min-h-[calc(100vh-64px)] flex items-center justify-center px-4">
          <div className="container mx-auto max-w-4xl text-center">
            {/* Member count badge */}
            <Badge className="mb-6 bg-green-500 text-white border-0 px-4 py-1.5 text-sm font-normal">
              <Users className="w-4 h-4 mr-2" />
              Join 15,244 Gaming members now
            </Badge>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Win Real Money Playing{" "}
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Skill-Based Games
              </span>
            </h1>

            <p className="text-lg md:text-xl text-white/80 mb-8 max-w-2xl mx-auto leading-relaxed">
              Master chess, sports betting, and prediction markets on the world's most advanced gaming platform.
              Compete against real players and win real rewards.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <SignUpButton mode="modal">
                <Button className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 rounded-xl font-bold text-lg hover:scale-105 transition-all">
                  Start Winning Now
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </SignUpButton>
              <Button variant="outline" className="px-8 py-4 text-white border-white hover:bg-white hover:text-black rounded-xl font-bold text-lg">
                Watch Demo
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 sm:gap-16">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-1">$125,099</div>
                <div className="text-sm text-white/70">Cashed Jackpot</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-1">15,244</div>
                <div className="text-sm text-white/70">Players Online</div>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Skill-Based Games Section */}
      <section id="games" className="py-20 bg-gradient-to-br from-slate-800 to-slate-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-white">Skill-Based Games</h2>
            <p className="text-slate-300 text-lg max-w-2xl mx-auto">
              Master strategic games and compete against real players for real rewards
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 max-w-7xl mx-auto">
            <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 transition-all hover:border-indigo-500 hover:shadow-xl hover:shadow-indigo-500/20">
              <div className="h-14 w-14 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center mb-6">
                <Trophy className="h-7 w-7 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Chess Masters</h3>
              <p className="text-slate-300 text-sm mb-4 leading-relaxed">Advanced chess with ELO ratings and tournaments</p>
              <ul className="space-y-1.5 text-xs text-slate-400">
                <li>• Rated gameplay</li>
                <li>• Weekly tournaments</li>
                <li>• Prize pools up to $10K</li>
              </ul>
            </div>

            <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 transition-all hover:border-amber-500 hover:shadow-xl hover:shadow-amber-500/20">
              <div className="h-14 w-14 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center mb-6">
                <Gamepad2 className="h-7 w-7 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Checkers Pro</h3>
              <p className="text-slate-300 text-sm mb-4 leading-relaxed">Classic strategy with modern competition</p>
              <ul className="space-y-1.5 text-xs text-slate-400">
                <li>• Fast-paced matches</li>
                <li>• Skill-based matchmaking</li>
                <li>• Daily challenges</li>
              </ul>
            </div>

            <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 transition-all hover:border-emerald-500 hover:shadow-xl hover:shadow-emerald-500/20">
              <div className="h-14 w-14 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center mb-6">
                <Zap className="h-7 w-7 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Highlight Hero</h3>
              <p className="text-slate-300 text-sm mb-4 leading-relaxed">Sports trivia and prediction challenges</p>
              <ul className="space-y-1.5 text-xs text-slate-400">
                <li>• Real-time events</li>
                <li>• Expert analysis</li>
                <li>• Live leaderboards</li>
              </ul>
            </div>

            <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 transition-all hover:border-red-500 hover:shadow-xl hover:shadow-red-500/20">
              <div className="h-14 w-14 rounded-lg bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center mb-6">
                <Target className="h-7 w-7 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Poker Rooms</h3>
              <p className="text-slate-300 text-sm mb-4 leading-relaxed">Texas Hold'em with guaranteed fairness</p>
              <ul className="space-y-1.5 text-xs text-slate-400">
                <li>• Multiple stakes</li>
                <li>• Verified randomness</li>
                <li>• Tournament series</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Sports & Prediction Markets */}
      <section id="sports" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-slate-900">Sports & Prediction Markets</h2>
            <p className="text-slate-600 text-lg max-w-2xl mx-auto">
              Bet on outcomes across sports, politics, crypto, and world events
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3 max-w-6xl mx-auto">
            <div className="bg-white border border-slate-200 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all hover:border-blue-300">
              <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mb-6 mx-auto">
                <Activity className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4 text-center">Live Sports Betting</h3>
              <p className="text-slate-600 mb-6 text-center leading-relaxed">
                Real-time odds on major leagues and events worldwide
              </p>
              <ul className="space-y-2 text-sm text-slate-600">
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  NFL, NBA, MLB, NHL
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  International Soccer
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Live In-Game Betting
                </li>
              </ul>
            </div>

            <div className="bg-white border border-slate-200 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all hover:border-amber-300">
              <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center mb-6 mx-auto">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4 text-center">Financial Markets</h3>
              <p className="text-slate-600 mb-6 text-center leading-relaxed">
                Trade on crypto, stocks, and commodity price movements
              </p>
              <ul className="space-y-2 text-sm text-slate-600">
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Bitcoin & Crypto Prices
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Stock Market Indices
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Gold & Oil Futures
                </li>
              </ul>
            </div>

            <div className="bg-white border border-slate-200 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all hover:border-emerald-300">
              <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center mb-6 mx-auto">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4 text-center">Prediction Markets</h3>
              <p className="text-slate-600 mb-6 text-center leading-relaxed">
                Forecast political, tech, and cultural events
              </p>
              <ul className="space-y-2 text-sm text-slate-600">
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Presidential Elections
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Tech IPOs & Launches
                </li>
                <li className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  Climate & Social Events
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-900 via-slate-900 to-indigo-900">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Start Winning?
          </h2>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Join thousands of skilled players already earning real money on BetBet
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <SignUpButton mode="modal">
              <Button className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 text-lg font-semibold rounded-lg transition-colors">
                Start Your Journey
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </SignUpButton>
            <Button variant="outline" className="text-white border-white hover:bg-white hover:text-slate-900 px-8 py-3 text-lg font-semibold rounded-lg">
              Learn More
            </Button>
          </div>

          <div className="flex justify-center gap-8 mt-12 text-sm text-slate-400">
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-green-400 mr-2" />
              SSL Secured
            </div>
            <div className="flex items-center">
              <Star className="h-5 w-5 text-green-400 mr-2" />
              Licensed & Regulated
            </div>
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-green-400 mr-2" />
              Instant Payouts
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}