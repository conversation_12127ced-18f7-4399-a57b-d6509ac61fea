"use client"

import { useState, useEffect, useCallback } from 'react'
import { walletApi, Wallet, Transaction, DepositRequest, DepositResponse } from '@/lib/wallet-api'
import { useToast } from '@/hooks/use-toast'

export interface UseWalletReturn {
  // Data
  wallets: Wallet[]
  transactions: Transaction[]
  selectedWallet: Wallet | null

  // Loading states
  loading: boolean
  depositing: boolean
  withdrawing: boolean
  transferring: boolean

  // Error states
  error: string | null

  // Actions
  refreshWallets: () => Promise<void>
  refreshTransactions: () => Promise<void>
  selectWallet: (wallet: Wallet) => void
  createWallet: (currency: string, type?: string) => Promise<void>
  initiateDeposit: (depositData: DepositRequest) => Promise<DepositResponse | null>
  requestWithdrawal: (walletId: string, amount: number, paymentMethod: string) => Promise<void>
  sendTransfer: (recipientEmail: string, amount: number, currency: string, memo?: string) => Promise<void>
  createSampleWallets: () => Promise<void>
}

export function useWallet(): UseWalletReturn {
  const [wallets, setWallets] = useState<Wallet[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null)

  const [loading, setLoading] = useState(false)
  const [depositing, setDepositing] = useState(false)
  const [withdrawing, setWithdrawing] = useState(false)
  const [transferring, setTransferring] = useState(false)

  const [error, setError] = useState<string | null>(null)

  const { toast } = useToast()

  const handleError = useCallback((err: any, action: string) => {
    const message = err.message || `Failed to ${action}`
    setError(message)
    toast({
      title: "Error",
      description: message,
      variant: "destructive"
    })
    console.error(`Wallet ${action} error:`, err)
  }, [toast])

  const refreshWallets = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const walletsData = await walletApi.getWallets()
      setWallets(walletsData)

      // Auto-select default wallet or first wallet
      if (walletsData.length > 0) {
        const defaultWallet = walletsData.find(w => w.is_default) || walletsData[0]
        setSelectedWallet(defaultWallet)
      }

    } catch (err) {
      handleError(err, 'load wallets')
    } finally {
      setLoading(false)
    }
  }, [handleError])

  const refreshTransactions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const transactionsData = await walletApi.getTransactions()
      setTransactions(transactionsData)

    } catch (err) {
      handleError(err, 'load transactions')
    } finally {
      setLoading(false)
    }
  }, [handleError])

  const selectWallet = useCallback((wallet: Wallet) => {
    setSelectedWallet(wallet)
  }, [])

  const createWallet = useCallback(async (currency: string, type: string = 'fiat') => {
    try {
      setLoading(true)
      setError(null)

      await walletApi.createWallet(currency, type)

      toast({
        title: "Success",
        description: `${currency} wallet created successfully`
      })

      // Refresh wallets to show the new one
      await refreshWallets()

    } catch (err) {
      handleError(err, 'create wallet')
    } finally {
      setLoading(false)
    }
  }, [handleError, refreshWallets, toast])

  const initiateDeposit = useCallback(async (depositData: DepositRequest): Promise<DepositResponse | null> => {
    try {
      setDepositing(true)
      setError(null)

      const response = await walletApi.initiateDeposit(depositData)

      toast({
        title: "Deposit Initiated",
        description: `Deposit of ${depositData.amount} ${depositData.currency} initiated successfully`
      })

      return response

    } catch (err) {
      handleError(err, 'initiate deposit')
      return null
    } finally {
      setDepositing(false)
    }
  }, [handleError, toast])

  const requestWithdrawal = useCallback(async (walletId: string, amount: number, paymentMethod: string) => {
    try {
      setWithdrawing(true)
      setError(null)

      await walletApi.requestWithdrawal(walletId, amount, paymentMethod)

      toast({
        title: "Withdrawal Requested",
        description: `Withdrawal of ${amount} requested successfully`
      })

      // Refresh data
      await Promise.all([refreshWallets(), refreshTransactions()])

    } catch (err) {
      handleError(err, 'request withdrawal')
    } finally {
      setWithdrawing(false)
    }
  }, [handleError, refreshWallets, refreshTransactions, toast])

  const sendTransfer = useCallback(async (recipientEmail: string, amount: number, currency: string, memo?: string) => {
    try {
      setTransferring(true)
      setError(null)

      await walletApi.sendTransfer(recipientEmail, amount, currency, memo)

      toast({
        title: "Transfer Sent",
        description: `Sent ${amount} ${currency} to ${recipientEmail}`
      })

      // Refresh data
      await Promise.all([refreshWallets(), refreshTransactions()])

    } catch (err) {
      handleError(err, 'send transfer')
    } finally {
      setTransferring(false)
    }
  }, [handleError, refreshWallets, refreshTransactions, toast])

  const createSampleWallets = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      await walletApi.createSampleWallets()

      toast({
        title: "Success",
        description: "Sample wallets created successfully"
      })

      // Refresh wallets to show the new ones
      await refreshWallets()

    } catch (err) {
      handleError(err, 'create sample wallets')
    } finally {
      setLoading(false)
    }
  }, [handleError, refreshWallets, toast])

  // Auto-load data on mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([refreshWallets(), refreshTransactions()])
    }

    loadInitialData()
  }, []) // Empty dependency array for mount only

  return {
    // Data
    wallets,
    transactions,
    selectedWallet,

    // Loading states
    loading,
    depositing,
    withdrawing,
    transferring,

    // Error states
    error,

    // Actions
    refreshWallets,
    refreshTransactions,
    selectWallet,
    createWallet,
    initiateDeposit,
    requestWithdrawal,
    sendTransfer,
    createSampleWallets,
  }
}