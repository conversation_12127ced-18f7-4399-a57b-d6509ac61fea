#!/bin/bash

# BetBet Kong Gateway Startup Script
set -e

echo "🚀 Starting BetBet Kong API Gateway..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Navigate to Kong directory
cd "$(dirname "$0")"

echo "📦 Starting Kong and dependencies with Docker Compose..."

# Start Kong services
docker-compose -f docker-compose.kong.yml up -d

echo "⏳ Waiting for Kong to be ready..."

# Wait for Kong to be healthy
MAX_ATTEMPTS=30
ATTEMPT=0

while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
    if curl -f -s http://localhost:8001 > /dev/null; then
        echo "✅ Kong is ready!"
        break
    fi

    echo "Waiting for Kong... (attempt $((ATTEMPT + 1))/$MAX_ATTEMPTS)"
    sleep 5
    ATTEMPT=$((ATTEMPT + 1))
done

if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
    echo "❌ Kong failed to start within expected time"
    echo "📋 Checking container logs:"
    docker-compose -f docker-compose.kong.yml logs kong
    exit 1
fi

echo "🔧 Configuring Kong services and routes..."
./kong-setup.sh

echo ""
echo "🎉 BetBet Kong API Gateway is now running!"
echo ""
echo "📊 Access Points:"
echo "• Kong Gateway (API): http://localhost:8080"
echo "• Kong Admin API: http://localhost:8001"
echo "• Kong Manager: http://localhost:8002"
echo "• Konga Admin UI: http://localhost:1337"
echo ""
echo "💡 Test the gateway:"
echo "curl http://localhost:8080/health"
echo ""