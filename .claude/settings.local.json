{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git remote add:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(brew install:*)", "Bash(HOMEBREW_NO_AUTO_UPDATE=1 brew install postgresql redis)", "<PERSON><PERSON>(createdb:*)", "Bash(PGPASSWORD=123Bubblegums createdb -U postgres betbet_main)", "<PERSON>sh(redis-server:*)", "Bash(redis-cli:*)", "Bash(pip install:*)", "Bash(python3 -m pip install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(npm install)", "Bash(PYTHONPATH=../.. alembic revision --autogenerate -m \"Initial migration - create all tables\")", "Bash(PGPASSWORD=123Bubblegums psql -U postgres -d betbet_main -c \"\\dt\")", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(sudo kill:*)", "Bash(kill:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(./kong-setup.sh:*)", "Bash(pg_isready:*)", "Read(//Users/<USER>/PycharmProjects/BetBet/frontend/src/components/games/chess/**)", "Read(//Users/<USER>/PycharmProjects/betbet-platform/frontend/web/app/**)", "Bash(npm install:*)", "Bash(brew services start:*)", "Bash(brew tap:*)", "<PERSON><PERSON>(python:*)", "Bash(brew services:*)", "Bash(PGPASSWORD=123Bubblegums psql -U postgres -h localhost -c \"\\l\")", "Bash(npm run dev:*)"], "deny": [], "ask": []}}