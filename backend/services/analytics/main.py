"""
BetBet Expert Analysis Service
Handles expert predictions, AI analysis, market insights, and analytics
"""

import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any
from decimal import Decimal

from fastapi import FastAPI, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func, desc
from sqlalchemy.orm import selectinload

import sys
sys.path.append('../..')
from shared.database import (
    init_all_databases, close_all_databases, get_db,
    get_redis_client
)
from shared.clerk_auth_simple import (
    Clerk<PERSON>ser, get_current_user, get_current_user_optional,
    require_kyc_level, require_expert_status
)
from shared.models import (
    UserProfile, Market, AnalysisReport,
    AnalysisCreate, AnalysisResponse
)


# Lifespan manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Starting Expert Analysis Service...")
    await init_all_databases()
    yield
    print("Shutting down Expert Analysis Service...")
    await close_all_databases()


# Create FastAPI app
app = FastAPI(
    title="BetBet Expert Analysis Service",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# =============================================================================
# Expert Analysis Endpoints
# =============================================================================

@app.post("/api/v1/analysis/create", response_model=AnalysisResponse)
async def create_analysis(
    analysis_data: AnalysisCreate,
    current_user: ClerkUser = Depends(require_expert_status),
    db: AsyncSession = Depends(get_db)
):
    """Create expert analysis - requires expert status"""
    try:
        # Create analysis report
        analysis = AnalysisReport(
            expert_user_id=current_user.id,
            market_id=uuid.UUID(analysis_data.market_id),
            title=analysis_data.title,
            content=analysis_data.content,
            prediction=analysis_data.prediction,
            confidence_score=analysis_data.confidence_score,
            analysis_type=analysis_data.analysis_type,
            tags=analysis_data.tags
        )

        db.add(analysis)
        await db.commit()
        await db.refresh(analysis)

        return analysis

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create analysis: {str(e)}")


@app.get("/api/v1/analysis/market/{market_id}")
async def get_market_analysis(
    market_id: str,
    current_user: Optional[ClerkUser] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """Get all analysis for a specific market"""
    try:
        market_uuid = uuid.UUID(market_id)

        result = await db.execute(
            select(AnalysisReport)
            .where(AnalysisReport.market_id == market_uuid)
            .order_by(AnalysisReport.created_at.desc())
        )
        analyses = result.scalars().all()

        return {
            "market_id": market_id,
            "analyses": [
                {
                    "id": str(analysis.id),
                    "expert_user_id": analysis.expert_user_id,
                    "title": analysis.title,
                    "content": analysis.content,
                    "prediction": analysis.prediction,
                    "confidence_score": float(analysis.confidence_score),
                    "analysis_type": analysis.analysis_type,
                    "tags": analysis.tags,
                    "created_at": analysis.created_at,
                    "updated_at": analysis.updated_at
                }
                for analysis in analyses
            ]
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid market ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analysis: {str(e)}")


@app.get("/api/v1/analysis/expert/{expert_id}")
async def get_expert_analysis(
    expert_id: str,
    current_user: Optional[ClerkUser] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """Get all analysis by a specific expert"""
    try:
        result = await db.execute(
            select(AnalysisReport)
            .where(AnalysisReport.expert_user_id == expert_id)
            .order_by(AnalysisReport.created_at.desc())
        )
        analyses = result.scalars().all()

        return {
            "expert_id": expert_id,
            "analyses": [
                {
                    "id": str(analysis.id),
                    "market_id": str(analysis.market_id),
                    "title": analysis.title,
                    "content": analysis.content,
                    "prediction": analysis.prediction,
                    "confidence_score": float(analysis.confidence_score),
                    "analysis_type": analysis.analysis_type,
                    "tags": analysis.tags,
                    "created_at": analysis.created_at,
                    "updated_at": analysis.updated_at
                }
                for analysis in analyses
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get expert analysis: {str(e)}")


@app.get("/api/v1/analysis/trending")
async def get_trending_analysis(
    limit: int = 10,
    current_user: Optional[ClerkUser] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """Get trending analysis based on engagement and recency"""
    try:
        # For now, just get recent popular analysis
        # In production, this would use engagement metrics
        result = await db.execute(
            select(AnalysisReport)
            .where(AnalysisReport.confidence_score >= 0.7)
            .order_by(AnalysisReport.created_at.desc())
            .limit(limit)
        )
        analyses = result.scalars().all()

        return {
            "trending_analyses": [
                {
                    "id": str(analysis.id),
                    "expert_user_id": analysis.expert_user_id,
                    "market_id": str(analysis.market_id),
                    "title": analysis.title,
                    "content": analysis.content[:200] + "..." if len(analysis.content) > 200 else analysis.content,
                    "prediction": analysis.prediction,
                    "confidence_score": float(analysis.confidence_score),
                    "analysis_type": analysis.analysis_type,
                    "tags": analysis.tags,
                    "created_at": analysis.created_at
                }
                for analysis in analyses
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get trending analysis: {str(e)}")


@app.post("/api/v1/analysis/ai-insights")
async def generate_ai_insights(
    market_id: str,
    current_user: ClerkUser = Depends(require_kyc_level(1)),
    db: AsyncSession = Depends(get_db)
):
    """Generate AI-powered market insights"""
    try:
        # This would integrate with OpenAI API
        # For now, return mock insights

        market_uuid = uuid.UUID(market_id)

        # Mock AI analysis
        insights = {
            "market_id": market_id,
            "ai_prediction": "FAVORABLE",
            "confidence": 0.78,
            "key_factors": [
                "Recent market trends show positive momentum",
                "Historical data suggests similar patterns led to favorable outcomes",
                "Current sentiment analysis indicates growing optimism"
            ],
            "risk_assessment": "MODERATE",
            "recommended_action": "Consider moderate position based on analysis",
            "generated_at": datetime.utcnow(),
            "model_version": "gpt-4-analysis-v1.0"
        }

        return insights

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid market ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate AI insights: {str(e)}")


@app.get("/api/v1/analytics/market-stats/{market_id}")
async def get_market_statistics(
    market_id: str,
    current_user: Optional[ClerkUser] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive market statistics and metrics"""
    try:
        market_uuid = uuid.UUID(market_id)

        # Mock statistics - in production would calculate from real data
        stats = {
            "market_id": market_id,
            "total_volume": 50000.00,
            "participant_count": 1247,
            "expert_consensus": "POSITIVE",
            "volatility_index": 0.34,
            "trend_direction": "UPWARD",
            "price_history": [
                {"timestamp": "2024-01-01T00:00:00Z", "price": 1.5},
                {"timestamp": "2024-01-02T00:00:00Z", "price": 1.7},
                {"timestamp": "2024-01-03T00:00:00Z", "price": 1.9},
            ],
            "sentiment_analysis": {
                "positive": 0.65,
                "negative": 0.25,
                "neutral": 0.10
            },
            "generated_at": datetime.utcnow()
        }

        return stats

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid market ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get market statistics: {str(e)}")


@app.get("/api/v1/analytics/expert-leaderboard")
async def get_expert_leaderboard(
    limit: int = 20,
    timeframe: str = "month",
    current_user: Optional[ClerkUser] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """Get expert leaderboard based on accuracy and performance"""
    try:
        # Mock leaderboard data
        # In production, this would calculate from actual prediction accuracy
        leaderboard = [
            {
                "expert_id": "expert_1",
                "username": "MarketGuru",
                "accuracy_rate": 0.87,
                "total_predictions": 156,
                "successful_predictions": 136,
                "reputation_score": 9.2,
                "followers": 2341
            },
            {
                "expert_id": "expert_2",
                "username": "CryptoWizard",
                "accuracy_rate": 0.84,
                "total_predictions": 203,
                "successful_predictions": 170,
                "reputation_score": 8.9,
                "followers": 1876
            },
            {
                "expert_id": "expert_3",
                "username": "SportsBetPro",
                "accuracy_rate": 0.81,
                "total_predictions": 289,
                "successful_predictions": 234,
                "reputation_score": 8.7,
                "followers": 3021
            }
        ]

        return {
            "timeframe": timeframe,
            "leaderboard": leaderboard[:limit],
            "generated_at": datetime.utcnow()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get expert leaderboard: {str(e)}")


# =============================================================================
# Real-time Analytics WebSocket
# =============================================================================

@app.websocket("/api/v1/analytics/live/{market_id}")
async def market_analytics_websocket(websocket: WebSocket, market_id: str):
    """Real-time market analytics via WebSocket"""
    await websocket.accept()

    try:
        # Mock real-time data stream
        import asyncio
        import random

        while True:
            # Send mock real-time analytics
            analytics_update = {
                "market_id": market_id,
                "timestamp": datetime.utcnow().isoformat(),
                "current_price": round(random.uniform(1.0, 3.0), 2),
                "volume_24h": round(random.uniform(10000, 100000), 2),
                "price_change_24h": round(random.uniform(-0.2, 0.2), 4),
                "active_traders": random.randint(50, 500),
                "sentiment_score": round(random.uniform(0.3, 0.9), 2)
            }

            await websocket.send_json(analytics_update)
            await asyncio.sleep(5)  # Update every 5 seconds

    except WebSocketDisconnect:
        print(f"WebSocket disconnected for market {market_id}")
    except Exception as e:
        print(f"WebSocket error for market {market_id}: {e}")
        await websocket.close()


# =============================================================================
# Health Check
# =============================================================================

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "expert_analysis"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8003,
        reload=True
    )