version: '3.8'

services:
  # PostgreSQL for Kong
  kong-db:
    image: postgres:15-alpine
    container_name: betbet-kong-db
    environment:
      POSTGRES_DB: kong
      POSTGRES_USER: kong
      POSTGRES_PASSWORD: kongpass
    volumes:
      - kong-db-data:/var/lib/postgresql/data
    networks:
      - betbet-network
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kong database migration
  kong-migration:
    image: kong:latest
    container_name: betbet-kong-migration
    command: kong migrations bootstrap
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-db
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kongpass
    depends_on:
      kong-db:
        condition: service_healthy
    networks:
      - betbet-network
    restart: on-failure

  # Kong Gateway
  kong:
    image: kong:latest
    container_name: betbet-kong-gateway
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-db
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kongpass
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
      KONG_PROXY_LISTEN: 0.0.0.0:8000
    ports:
      - "8080:8000"   # Kong Proxy (API Gateway)
      - "8001:8001"   # Kong Admin API
      - "8002:8002"   # Kong Manager (GUI)
    depends_on:
      - kong-migration
    networks:
      - betbet-network
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 10s
      retries: 10

  # Konga (Kong Admin UI)
  konga:
    image: pantsel/konga
    platform: linux/amd64
    container_name: betbet-konga
    environment:
      NODE_ENV: development
      KONGA_HOOK_TIMEOUT: 10000
    ports:
      - "1337:1337"
    depends_on:
      - kong
    networks:
      - betbet-network

volumes:
  kong-db-data:

networks:
  betbet-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16