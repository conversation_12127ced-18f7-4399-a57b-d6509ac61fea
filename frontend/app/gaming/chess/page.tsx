'use client';

import React, { useState } from 'react';
import { useAuth, useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ChessGame from '@/components/games/chess/ChessGame';

export default function ChessPage() {
  const { getToken } = useAuth();
  const { user } = useUser();
  const [gameState, setGameState] = useState<'lobby' | 'creating' | 'playing'>('lobby');
  const [gameConfig, setGameConfig] = useState({
    timeControl: 'Blitz',
    wagerAmount: 10,
    isRated: true,
    playerColor: 'random'
  });

  const handleCreateGame = async () => {
    setGameState('creating');

    try {
      // Get the authentication token from Clerk
      const token = await getToken();

      if (!token) {
        alert('You must be logged in to create a game');
        setGameState('lobby');
        return;
      }

      const response = await fetch('http://localhost:8001/api/v1/chess/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          timeControl: gameConfig.timeControl,
          wagerAmount: gameConfig.wagerAmount,
          isRated: gameConfig.isRated,
          playerColor: gameConfig.playerColor,
          inviteType: 'open',
          isPrivate: false,
          requirePassword: false,
          variant: 'standard',
          allowSpectators: true,
          allowChat: true,
          autoStart: false,
          isTournament: false
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Game created successfully:', result);
        setGameState('playing');
      } else {
        const error = await response.json();
        console.error('Failed to create game:', error);
        alert('Failed to create game: ' + (error.detail || 'Unknown error'));
        setGameState('lobby');
      }
    } catch (error) {
      console.error('Error creating game:', error);
      alert('Error creating game: ' + error);
      setGameState('lobby');
    }
  };

  const handleBackToLobby = () => {
    setGameState('lobby');
  };

  if (gameState === 'playing') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto py-4">
          <div className="mb-4">
            <Button onClick={handleBackToLobby} variant="outline">
              ← Back to Chess Lobby
            </Button>
          </div>
          <ChessGame
            onGameEnd={(result) => {
              console.log('Game ended:', result);
              setGameState('lobby');
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Chess Arena</h1>
        <p className="text-muted-foreground text-lg">Challenge players in real-time chess matches with wagering</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Quick Play */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle>Quick Play</CardTitle>
            <CardDescription>Start a chess game instantly</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Time Control</label>
                <select
                  value={gameConfig.timeControl}
                  onChange={(e) => setGameConfig(prev => ({ ...prev, timeControl: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Bullet">Bullet (1 min)</option>
                  <option value="Blitz">Blitz (3 min)</option>
                  <option value="Rapid">Rapid (10 min)</option>
                  <option value="Classical">Classical (30 min)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Wager Amount ($)</label>
                <input
                  type="number"
                  value={gameConfig.wagerAmount}
                  onChange={(e) => setGameConfig(prev => ({ ...prev, wagerAmount: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  step="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Player Color</label>
                <select
                  value={gameConfig.playerColor}
                  onChange={(e) => setGameConfig(prev => ({ ...prev, playerColor: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="random">Random</option>
                  <option value="white">White</option>
                  <option value="black">Black</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="rated"
                  checked={gameConfig.isRated}
                  onChange={(e) => setGameConfig(prev => ({ ...prev, isRated: e.target.checked }))}
                  className="rounded"
                />
                <label htmlFor="rated" className="text-sm font-medium">Rated Game</label>
              </div>

              <Button
                onClick={handleCreateGame}
                className="w-full"
                disabled={gameState === 'creating'}
              >
                {gameState === 'creating' ? 'Creating Game...' : 'Create Game'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tournaments */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle>Tournaments</CardTitle>
            <CardDescription>Join or create chess tournaments</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              disabled
            >
              Coming Soon
            </Button>
          </CardContent>
        </Card>

        {/* Practice */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle>Practice Mode</CardTitle>
            <CardDescription>Play against AI to improve your skills</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              disabled
            >
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Test API Connection */}
      <Card>
        <CardHeader>
          <CardTitle>API Connection Test</CardTitle>
          <CardDescription>Test connection to the game engine</CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={async () => {
              try {
                const response = await fetch('http://localhost:8001/health');
                const result = await response.json();
                alert('API Connection: ' + JSON.stringify(result));
              } catch (error) {
                alert('API Connection Failed: ' + error);
              }
            }}
            variant="outline"
          >
            Test API Connection
          </Button>
        </CardContent>
      </Card>

      {/* Recent Games */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Games</CardTitle>
          <CardDescription>Your chess game history</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">No recent games found. Create your first game to get started!</p>
        </CardContent>
      </Card>
    </div>
  );
}