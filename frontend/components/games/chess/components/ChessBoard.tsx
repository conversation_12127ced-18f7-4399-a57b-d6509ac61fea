'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { ChessGameState, ChessMove, ChessPiece, ChessSquare } from '../types';

interface ChessBoardProps {
  gameState: ChessGameState;
  isPlayerTurn: boolean;
  playerColor: 'white' | 'black';
  onMove: (move: { from: ChessSquare; to: ChessSquare; promotion?: string }) => void;
  disabled?: boolean;
}

const ChessBoard: React.FC<ChessBoardProps> = ({
  gameState,
  isPlayerTurn,
  playerColor,
  onMove,
  disabled = false
}) => {
  const [selectedSquare, setSelectedSquare] = useState<ChessSquare | null>(null);
  const [highlightedSquares, setHighlightedSquares] = useState<Set<ChessSquare>>(new Set());
  const [lastMove, setLastMove] = useState<{ from: ChessSquare; to: ChessSquare } | null>(null);

  // Parse FEN to get board position
  const parseFen = useCallback((fen: string) => {
    const board: { [key: string]: ChessPiece } = {};
    const [position] = fen.split(' ');
    const rows = position.split('/');

    for (let rank = 0; rank < 8; rank++) {
      let file = 0;
      for (const char of rows[rank]) {
        if (char >= '1' && char <= '8') {
          file += parseInt(char);
        } else {
          const square = `${String.fromCharCode(97 + file)}${8 - rank}` as ChessSquare;
          board[square] = char as ChessPiece;
          file++;
        }
      }
    }

    return board;
  }, []);

  const boardPosition = parseFen(gameState.boardFen);

  // Update last move when move history changes
  useEffect(() => {
    if (gameState.moveHistory.length > 0) {
      const lastMoveInHistory = gameState.moveHistory[gameState.moveHistory.length - 1];
      const uci = lastMoveInHistory.moveUci;
      if (uci && uci.length >= 4) {
        setLastMove({
          from: uci.slice(0, 2) as ChessSquare,
          to: uci.slice(2, 4) as ChessSquare
        });
      }
    }
  }, [gameState.moveHistory]);

  const handleSquareClick = useCallback((square: ChessSquare) => {
    if (disabled || !isPlayerTurn) return;

    if (selectedSquare === square) {
      setSelectedSquare(null);
      setHighlightedSquares(new Set());
      return;
    }

    if (selectedSquare) {
      // Attempt to make a move
      onMove({ from: selectedSquare, to: square });
      setSelectedSquare(null);
      setHighlightedSquares(new Set());
    } else {
      // Select a square
      const piece = boardPosition[square];
      if (piece) {
        const isWhitePiece = piece === piece.toUpperCase();
        const isPlayerPiece = (playerColor === 'white' && isWhitePiece) ||
                             (playerColor === 'black' && !isWhitePiece);

        if (isPlayerPiece) {
          setSelectedSquare(square);
          // Highlight legal moves (simplified - would need chess engine for accurate legal moves)
          const legalDestinations = gameState.legalMoves
            .filter(move => move.startsWith(square))
            .map(move => move.slice(2, 4) as ChessSquare);
          setHighlightedSquares(new Set(legalDestinations));
        }
      }
    }
  }, [selectedSquare, boardPosition, playerColor, isPlayerTurn, disabled, onMove, gameState.legalMoves]);

  const getPieceSymbol = (piece: ChessPiece): string => {
    if (!piece) return '';

    const symbols: { [key: string]: string } = {
      'K': '♔', 'Q': '♕', 'R': '♖', 'B': '♗', 'N': '♘', 'P': '♙',
      'k': '♚', 'q': '♛', 'r': '♜', 'b': '♝', 'n': '♞', 'p': '♟'
    };

    return symbols[piece] || '';
  };

  const getSquareColor = (file: number, rank: number): string => {
    return (file + rank) % 2 === 0 ? 'bg-amber-100' : 'bg-amber-800';
  };

  const getSquareClasses = (square: ChessSquare, file: number, rank: number): string => {
    let classes = `${getSquareColor(file, rank)} aspect-square flex items-center justify-center cursor-pointer transition-all duration-200 relative`;

    if (selectedSquare === square) {
      classes += ' ring-4 ring-blue-400';
    }

    if (highlightedSquares.has(square)) {
      classes += ' ring-2 ring-green-400';
    }

    if (lastMove && (lastMove.from === square || lastMove.to === square)) {
      classes += ' ring-2 ring-yellow-400';
    }

    if (gameState.inCheck && boardPosition[square]?.toLowerCase() === 'k') {
      const isWhiteKing = boardPosition[square] === 'K';
      const isPlayerKing = (playerColor === 'white' && isWhiteKing) ||
                          (playerColor === 'black' && !isWhiteKing);
      if (isPlayerKing) {
        classes += ' ring-4 ring-red-500';
      }
    }

    if (disabled || !isPlayerTurn) {
      classes += ' opacity-75';
    }

    return classes;
  };

  const renderSquare = (square: ChessSquare, file: number, rank: number) => {
    const piece = boardPosition[square];
    const isHighlighted = highlightedSquares.has(square);

    return (
      <div
        key={square}
        className={getSquareClasses(square, file, rank)}
        onClick={() => handleSquareClick(square)}
      >
        {/* Coordinate labels */}
        {file === 0 && (
          <div className="absolute top-1 left-1 text-xs font-bold text-gray-600">
            {playerColor === 'white' ? 8 - rank : rank + 1}
          </div>
        )}
        {rank === 7 && (
          <div className="absolute bottom-1 right-1 text-xs font-bold text-gray-600">
            {String.fromCharCode(97 + (playerColor === 'white' ? file : 7 - file))}
          </div>
        )}

        {/* Piece */}
        {piece && (
          <div className="text-4xl select-none">
            {getPieceSymbol(piece)}
          </div>
        )}

        {/* Move hint dot */}
        {isHighlighted && !piece && (
          <div className="w-4 h-4 bg-green-500 rounded-full opacity-60" />
        )}

        {/* Capture hint */}
        {isHighlighted && piece && (
          <div className="absolute inset-0 border-4 border-green-500 rounded-full opacity-60" />
        )}
      </div>
    );
  };

  const squares = [];
  for (let rank = 0; rank < 8; rank++) {
    for (let file = 0; file < 8; file++) {
      const actualRank = playerColor === 'white' ? rank : 7 - rank;
      const actualFile = playerColor === 'white' ? file : 7 - file;
      const square = `${String.fromCharCode(97 + actualFile)}${8 - actualRank}` as ChessSquare;
      squares.push(renderSquare(square, actualFile, actualRank));
    }
  }

  return (
    <div className="relative">
      {/* Game Status */}
      <div className="mb-4 text-center">
        <div className="text-lg font-semibold">
          {gameState.gameEnded ? (
            <span className="text-red-600">Game Ended - {gameState.gameResult}</span>
          ) : (
            <span className={isPlayerTurn ? 'text-green-600' : 'text-gray-500'}>
              {isPlayerTurn ? 'Your Turn' : "Opponent's Turn"}
            </span>
          )}
        </div>
        {gameState.inCheck && (
          <div className="text-red-500 font-bold">CHECK!</div>
        )}
      </div>

      {/* Chess Board */}
      <div className="grid grid-cols-8 gap-0 border-4 border-amber-900 shadow-lg max-w-lg mx-auto">
        {squares}
      </div>

      {/* Game Info */}
      <div className="mt-4 flex justify-between text-sm">
        <div>
          <div className="font-semibold">Move: {gameState.moveNumber}</div>
          <div>Playing as: {playerColor}</div>
        </div>
        <div className="text-right">
          <div>White: {Math.floor(gameState.whiteTime / 60)}:{String(gameState.whiteTime % 60).padStart(2, '0')}</div>
          <div>Black: {Math.floor(gameState.blackTime / 60)}:{String(gameState.blackTime % 60).padStart(2, '0')}</div>
        </div>
      </div>
    </div>
  );
};

export default ChessBoard;