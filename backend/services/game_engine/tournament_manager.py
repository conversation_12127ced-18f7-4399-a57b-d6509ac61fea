"""
Tournament Management System for BetBet Gaming Platform
Handles tournament creation, bracket generation, and match orchestration
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from tournament_models import (
    Tournament, TournamentParticipant, TournamentBracket, BracketMatch,
    TournamentStatus, TournamentFormat, BracketMatchStatus, TournamentPrizeStructure
)
import redis.asyncio as redis
import math


class TournamentManager:
    """Manages tournaments and bracket systems"""

    def __init__(self, db_session: AsyncSession, redis_url: str = "redis://localhost:6379"):
        self.db_session = db_session
        self.redis_url = redis_url
        self.redis_client = None

    async def init_redis(self):
        """Initialize Redis connection"""
        if not self.redis_client:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)

    async def create_tournament(self, tournament_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new tournament"""
        try:
            await self.init_redis()

            # Create tournament record
            tournament = Tournament(
                name=tournament_data["name"],
                description=tournament_data.get("description", ""),
                game_type=tournament_data["game_type"],
                format=TournamentFormat(tournament_data["format"]),
                max_participants=tournament_data["max_participants"],
                entry_fee=tournament_data.get("entry_fee", 0.0),
                currency=tournament_data.get("currency", "USD"),
                registration_end=datetime.fromisoformat(tournament_data["registration_end"]),
                tournament_start=datetime.fromisoformat(tournament_data["tournament_start"]),
                game_settings=json.dumps(tournament_data.get("game_settings", {})),
                is_rated=tournament_data.get("is_rated", True),
                allow_spectators=tournament_data.get("allow_spectators", True),
                allow_chat=tournament_data.get("allow_chat", True),
                creator_id=tournament_data["creator_id"],
                organizer_name=tournament_data.get("organizer_name", "")
            )

            self.db_session.add(tournament)
            await self.db_session.commit()

            # Calculate prize pool if entry fees are involved
            if tournament.entry_fee > 0:
                tournament.prize_pool = tournament.max_participants * tournament.entry_fee * 0.95  # 5% platform fee
                await self.db_session.commit()

            # Cache tournament in Redis for quick access
            await self._cache_tournament(tournament)

            return {
                "status": "success",
                "tournament_id": tournament.id,
                "tournament": self._tournament_to_dict(tournament)
            }

        except Exception as e:
            await self.db_session.rollback()
            return {"error": f"Failed to create tournament: {str(e)}"}

    async def register_participant(self, tournament_id: str, player_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register a participant for a tournament"""
        try:
            # Get tournament
            result = await self.db_session.execute(
                select(Tournament).where(Tournament.id == tournament_id)
            )
            tournament = result.scalar_one_or_none()

            if not tournament:
                return {"error": "Tournament not found"}

            if tournament.status != TournamentStatus.REGISTRATION:
                return {"error": "Tournament registration is closed"}

            # Check if already registered
            existing_result = await self.db_session.execute(
                select(TournamentParticipant).where(
                    and_(
                        TournamentParticipant.tournament_id == tournament_id,
                        TournamentParticipant.player_id == player_data["player_id"]
                    )
                )
            )
            if existing_result.scalar_one_or_none():
                return {"error": "Player already registered"}

            # Check max participants
            participants_result = await self.db_session.execute(
                select(TournamentParticipant).where(
                    TournamentParticipant.tournament_id == tournament_id
                )
            )
            current_participants = participants_result.scalars().all()

            if len(current_participants) >= tournament.max_participants:
                return {"error": "Tournament is full"}

            # Create participant record
            participant = TournamentParticipant(
                tournament_id=tournament_id,
                player_id=player_data["player_id"],
                player_name=player_data["player_name"],
                rating=player_data.get("rating")
            )

            self.db_session.add(participant)
            await self.db_session.commit()

            # Update Redis cache
            await self._cache_tournament_participant(tournament_id, participant)

            return {
                "status": "success",
                "message": "Successfully registered for tournament",
                "participant_id": participant.id
            }

        except Exception as e:
            await self.db_session.rollback()
            return {"error": f"Failed to register participant: {str(e)}"}

    async def start_tournament(self, tournament_id: str) -> Dict[str, Any]:
        """Start a tournament and generate brackets"""
        try:
            # Get tournament and participants
            tournament_result = await self.db_session.execute(
                select(Tournament).where(Tournament.id == tournament_id)
            )
            tournament = tournament_result.scalar_one_or_none()

            if not tournament:
                return {"error": "Tournament not found"}

            if tournament.status != TournamentStatus.REGISTRATION:
                return {"error": "Tournament cannot be started"}

            # Get participants
            participants_result = await self.db_session.execute(
                select(TournamentParticipant).where(
                    TournamentParticipant.tournament_id == tournament_id
                )
            )
            participants = participants_result.scalars().all()

            if len(participants) < 2:
                return {"error": "Not enough participants to start tournament"}

            # Generate seeding
            await self._assign_seeds(participants)

            # Generate bracket structure
            bracket_structure = await self._generate_brackets(tournament, participants)

            # Update tournament status
            tournament.status = TournamentStatus.ACTIVE
            tournament.tournament_start = datetime.utcnow()
            await self.db_session.commit()

            # Start first round matches
            await self._start_round_matches(tournament_id, 1)

            return {
                "status": "success",
                "message": "Tournament started successfully",
                "brackets": bracket_structure
            }

        except Exception as e:
            await self.db_session.rollback()
            return {"error": f"Failed to start tournament: {str(e)}"}

    async def _assign_seeds(self, participants: List[TournamentParticipant]):
        """Assign seed numbers to participants based on rating"""
        # Sort by rating (descending), then by registration time
        sorted_participants = sorted(
            participants,
            key=lambda p: (p.rating or 0, -p.registered_at.timestamp()),
            reverse=True
        )

        for i, participant in enumerate(sorted_participants):
            participant.seed_number = i + 1

        await self.db_session.commit()

    async def _generate_brackets(self, tournament: Tournament, participants: List[TournamentParticipant]) -> Dict[str, Any]:
        """Generate tournament brackets based on format"""
        if tournament.format == TournamentFormat.SINGLE_ELIMINATION:
            return await self._generate_single_elimination_brackets(tournament, participants)
        elif tournament.format == TournamentFormat.DOUBLE_ELIMINATION:
            return await self._generate_double_elimination_brackets(tournament, participants)
        elif tournament.format == TournamentFormat.ROUND_ROBIN:
            return await self._generate_round_robin_brackets(tournament, participants)
        else:
            raise ValueError(f"Unsupported tournament format: {tournament.format}")

    async def _generate_single_elimination_brackets(self, tournament: Tournament, participants: List[TournamentParticipant]) -> Dict[str, Any]:
        """Generate single elimination bracket structure"""
        num_participants = len(participants)
        num_rounds = math.ceil(math.log2(num_participants))

        # Create bracket positions
        brackets = {}
        matches = []

        # Generate first round matches
        sorted_participants = sorted(participants, key=lambda p: p.seed_number)

        # Pair participants for first round
        first_round_matches = []
        i = 0
        while i < len(sorted_participants):
            if i + 1 < len(sorted_participants):
                # Pair current participant with next
                first_round_matches.append((sorted_participants[i], sorted_participants[i + 1]))
                i += 2
            else:
                # Bye for last participant
                first_round_matches.append((sorted_participants[i], None))
                i += 1

        # Create matches for first round
        for match_num, (p1, p2) in enumerate(first_round_matches):
            match = BracketMatch(
                tournament_id=tournament.id,
                round_number=1,
                match_number=match_num + 1,
                player1_id=p1.player_id,
                player1_name=p1.player_name,
                player1_seed=p1.seed_number,
                player2_id=p2.player_id if p2 else None,
                player2_name=p2.player_name if p2 else "BYE",
                player2_seed=p2.seed_number if p2 else None,
                status=BracketMatchStatus.PENDING if p2 else BracketMatchStatus.COMPLETED,
                winner_id=p1.player_id if not p2 else None  # Bye wins automatically
            )

            self.db_session.add(match)
            matches.append(match)

        # Generate subsequent round placeholders
        for round_num in range(2, num_rounds + 1):
            matches_in_round = math.ceil(len(first_round_matches) / (2 ** (round_num - 1)))
            for match_num in range(matches_in_round):
                match = BracketMatch(
                    tournament_id=tournament.id,
                    round_number=round_num,
                    match_number=match_num + 1,
                    status=BracketMatchStatus.PENDING
                )
                self.db_session.add(match)
                matches.append(match)

        await self.db_session.commit()

        brackets["single_elimination"] = {
            "rounds": num_rounds,
            "matches": [self._match_to_dict(match) for match in matches]
        }

        return brackets

    async def _generate_double_elimination_brackets(self, tournament: Tournament, participants: List[TournamentParticipant]) -> Dict[str, Any]:
        """Generate double elimination bracket structure"""
        # First generate winners bracket (same as single elimination)
        winners_bracket = await self._generate_single_elimination_brackets(tournament, participants)

        # Then generate losers bracket structure
        # This is more complex and requires careful positioning
        # For now, implement basic structure

        return {
            "winners_bracket": winners_bracket["single_elimination"],
            "losers_bracket": {"rounds": 0, "matches": []}  # Simplified for now
        }

    async def _generate_round_robin_brackets(self, tournament: Tournament, participants: List[TournamentParticipant]) -> Dict[str, Any]:
        """Generate round robin bracket structure"""
        num_participants = len(participants)
        matches = []

        # Create matches for every participant pair
        match_num = 1
        for i in range(num_participants):
            for j in range(i + 1, num_participants):
                match = BracketMatch(
                    tournament_id=tournament.id,
                    round_number=1,  # All matches in round 1 for round robin
                    match_number=match_num,
                    player1_id=participants[i].player_id,
                    player1_name=participants[i].player_name,
                    player1_seed=participants[i].seed_number,
                    player2_id=participants[j].player_id,
                    player2_name=participants[j].player_name,
                    player2_seed=participants[j].seed_number,
                    status=BracketMatchStatus.PENDING
                )
                self.db_session.add(match)
                matches.append(match)
                match_num += 1

        await self.db_session.commit()

        return {
            "round_robin": {
                "rounds": 1,
                "total_matches": len(matches),
                "matches": [self._match_to_dict(match) for match in matches]
            }
        }

    async def _start_round_matches(self, tournament_id: str, round_number: int):
        """Start matches for a specific round"""
        matches_result = await self.db_session.execute(
            select(BracketMatch).where(
                and_(
                    BracketMatch.tournament_id == tournament_id,
                    BracketMatch.round_number == round_number,
                    BracketMatch.status == BracketMatchStatus.PENDING
                )
            )
        )
        matches = matches_result.scalars().all()

        for match in matches:
            if match.player1_id and match.player2_id:  # Both players present
                match.status = BracketMatchStatus.ACTIVE
                match.scheduled_start = datetime.utcnow()
                # Here you would typically create the actual game session
                # and link it to the match

        await self.db_session.commit()

    async def complete_match(self, match_id: str, winner_id: str, result: str) -> Dict[str, Any]:
        """Complete a tournament match and advance bracket"""
        try:
            # Get match
            match_result = await self.db_session.execute(
                select(BracketMatch).where(BracketMatch.id == match_id)
            )
            match = match_result.scalar_one_or_none()

            if not match:
                return {"error": "Match not found"}

            if match.status == BracketMatchStatus.COMPLETED:
                return {"error": "Match already completed"}

            # Update match
            match.winner_id = winner_id
            match.loser_id = match.player1_id if winner_id == match.player2_id else match.player2_id
            match.result = result
            match.status = BracketMatchStatus.COMPLETED
            match.completed_at = datetime.utcnow()

            await self.db_session.commit()

            # Advance winner to next round
            await self._advance_winner(match)

            # Check if tournament is complete
            await self._check_tournament_completion(match.tournament_id)

            return {
                "status": "success",
                "message": "Match completed successfully",
                "winner_id": winner_id
            }

        except Exception as e:
            await self.db_session.rollback()
            return {"error": f"Failed to complete match: {str(e)}"}

    async def _advance_winner(self, completed_match: BracketMatch):
        """Advance winner to next round match"""
        if not completed_match.winner_id:
            return

        # Find next round match where this winner should be placed
        next_round_result = await self.db_session.execute(
            select(BracketMatch).where(
                and_(
                    BracketMatch.tournament_id == completed_match.tournament_id,
                    BracketMatch.round_number == completed_match.round_number + 1
                )
            )
        )
        next_round_matches = next_round_result.scalars().all()

        # Find appropriate slot based on match number
        target_match_num = math.ceil(completed_match.match_number / 2)
        target_match = next((m for m in next_round_matches if m.match_number == target_match_num), None)

        if target_match:
            # Determine if winner goes to player1 or player2 slot
            if completed_match.match_number % 2 == 1:  # Odd match number -> player1
                if not target_match.player1_id:
                    target_match.player1_id = completed_match.winner_id
                    target_match.player1_name = self._get_player_name(completed_match, completed_match.winner_id)
            else:  # Even match number -> player2
                if not target_match.player2_id:
                    target_match.player2_id = completed_match.winner_id
                    target_match.player2_name = self._get_player_name(completed_match, completed_match.winner_id)

            # If both players are now set, make match active
            if target_match.player1_id and target_match.player2_id:
                target_match.status = BracketMatchStatus.ACTIVE
                target_match.scheduled_start = datetime.utcnow()

        await self.db_session.commit()

    def _get_player_name(self, match: BracketMatch, player_id: str) -> str:
        """Get player name from match"""
        if match.player1_id == player_id:
            return match.player1_name
        elif match.player2_id == player_id:
            return match.player2_name
        return "Unknown"

    async def _check_tournament_completion(self, tournament_id: str):
        """Check if tournament is complete and handle prize distribution"""
        # Get tournament
        tournament_result = await self.db_session.execute(
            select(Tournament).where(Tournament.id == tournament_id)
        )
        tournament = tournament_result.scalar_one_or_none()

        if not tournament or tournament.status == TournamentStatus.COMPLETED:
            return

        # Check if all matches are completed
        active_matches_result = await self.db_session.execute(
            select(BracketMatch).where(
                and_(
                    BracketMatch.tournament_id == tournament_id,
                    BracketMatch.status.in_([BracketMatchStatus.PENDING, BracketMatchStatus.ACTIVE])
                )
            )
        )
        active_matches = active_matches_result.scalars().all()

        if len(active_matches) == 0:
            # Tournament is complete
            tournament.status = TournamentStatus.COMPLETED
            tournament.tournament_end = datetime.utcnow()

            # Distribute prizes
            await self._distribute_prizes(tournament)

            await self.db_session.commit()

    async def _distribute_prizes(self, tournament: Tournament):
        """Distribute tournament prizes"""
        if tournament.prize_pool <= 0:
            return

        # Get final standings
        final_matches = await self._get_final_standings(tournament.id)

        # Simple prize distribution (winner takes all for now)
        # In a real system, you'd have more sophisticated prize structures
        if final_matches:
            winner_id = final_matches[0].winner_id
            if winner_id:
                # Update participant with prize
                participant_result = await self.db_session.execute(
                    select(TournamentParticipant).where(
                        and_(
                            TournamentParticipant.tournament_id == tournament.id,
                            TournamentParticipant.player_id == winner_id
                        )
                    )
                )
                participant = participant_result.scalar_one_or_none()

                if participant:
                    participant.prize_amount = tournament.prize_pool
                    participant.prize_currency = tournament.currency
                    participant.final_position = 1

    async def _get_final_standings(self, tournament_id: str) -> List[BracketMatch]:
        """Get final standings from tournament matches"""
        # For single elimination, the highest round completed match determines winner
        final_round_result = await self.db_session.execute(
            select(BracketMatch).where(
                and_(
                    BracketMatch.tournament_id == tournament_id,
                    BracketMatch.status == BracketMatchStatus.COMPLETED
                )
            ).order_by(BracketMatch.round_number.desc(), BracketMatch.match_number)
        )
        return final_round_result.scalars().all()

    async def _cache_tournament(self, tournament: Tournament):
        """Cache tournament data in Redis"""
        await self.init_redis()
        tournament_data = self._tournament_to_dict(tournament)
        await self.redis_client.hset(
            f"tournament:{tournament.id}",
            mapping={k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                    for k, v in tournament_data.items()}
        )
        await self.redis_client.expire(f"tournament:{tournament.id}", 3600 * 24)

    async def _cache_tournament_participant(self, tournament_id: str, participant: TournamentParticipant):
        """Cache tournament participant in Redis"""
        await self.init_redis()
        await self.redis_client.sadd(f"tournament_participants:{tournament_id}", participant.player_id)
        await self.redis_client.expire(f"tournament_participants:{tournament_id}", 3600 * 24)

    def _tournament_to_dict(self, tournament: Tournament) -> Dict[str, Any]:
        """Convert tournament to dictionary"""
        return {
            "id": tournament.id,
            "name": tournament.name,
            "description": tournament.description,
            "game_type": tournament.game_type,
            "format": tournament.format.value,
            "status": tournament.status.value,
            "max_participants": tournament.max_participants,
            "entry_fee": tournament.entry_fee,
            "prize_pool": tournament.prize_pool,
            "currency": tournament.currency,
            "registration_end": tournament.registration_end.isoformat(),
            "tournament_start": tournament.tournament_start.isoformat(),
            "is_rated": tournament.is_rated,
            "allow_spectators": tournament.allow_spectators,
            "creator_id": tournament.creator_id,
            "created_at": tournament.created_at.isoformat()
        }

    def _match_to_dict(self, match: BracketMatch) -> Dict[str, Any]:
        """Convert match to dictionary"""
        return {
            "id": match.id,
            "round_number": match.round_number,
            "match_number": match.match_number,
            "status": match.status.value,
            "player1_id": match.player1_id,
            "player1_name": match.player1_name,
            "player1_seed": match.player1_seed,
            "player2_id": match.player2_id,
            "player2_name": match.player2_name,
            "player2_seed": match.player2_seed,
            "winner_id": match.winner_id,
            "result": match.result,
            "scheduled_start": match.scheduled_start.isoformat() if match.scheduled_start else None,
            "completed_at": match.completed_at.isoformat() if match.completed_at else None
        }