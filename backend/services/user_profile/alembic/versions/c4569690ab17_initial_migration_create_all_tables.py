"""Initial migration - create all tables

Revision ID: c4569690ab17
Revises: 
Create Date: 2025-09-14 13:00:25.779769

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c4569690ab17'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('games',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=False),
    sa.Column('subcategory', sa.String(length=50), nullable=True),
    sa.Column('game_type', sa.String(length=50), nullable=True),
    sa.Column('min_players', sa.Integer(), nullable=True),
    sa.Column('max_players', sa.Integer(), nullable=True),
    sa.Column('rules', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('thumbnail_url', sa.String(length=500), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_profiles',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('clerk_user_id', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('display_name', sa.String(length=100), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('country_code', sa.String(length=2), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=True),
    sa.Column('language', sa.String(length=10), nullable=True),
    sa.Column('skill_rating', sa.Integer(), nullable=True),
    sa.Column('total_earnings', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('total_wagered', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('verified_expert', sa.Boolean(), nullable=True),
    sa.Column('referral_code', sa.String(length=20), nullable=True),
    sa.Column('referred_by', sa.UUID(), nullable=True),
    sa.Column('settings', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_seen', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['referred_by'], ['user_profiles.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('referral_code'),
    sa.UniqueConstraint('username')
    )
    op.create_index(op.f('ix_user_profiles_clerk_user_id'), 'user_profiles', ['clerk_user_id'], unique=True)
    op.create_table('game_sessions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('game_id', sa.UUID(), nullable=True),
    sa.Column('session_code', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('stake_amount', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('player_count', sa.Integer(), nullable=True),
    sa.Column('max_players', sa.Integer(), nullable=True),
    sa.Column('is_private', sa.Boolean(), nullable=True),
    sa.Column('spectator_fee', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('created_by_clerk_id', sa.String(length=255), nullable=False),
    sa.Column('created_by_profile_id', sa.UUID(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('ended_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_profile_id'], ['user_profiles.id'], ),
    sa.ForeignKeyConstraint(['game_id'], ['games.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('session_code')
    )
    op.create_table('kyc_documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_profile_id', sa.UUID(), nullable=True),
    sa.Column('clerk_user_id', sa.String(length=255), nullable=False),
    sa.Column('document_type', sa.String(length=50), nullable=True),
    sa.Column('document_url', sa.String(length=500), nullable=True),
    sa.Column('kyc_level', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('verified_at', sa.DateTime(), nullable=True),
    sa.Column('verified_by', sa.UUID(), nullable=True),
    sa.Column('document_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_profile_id'], ['user_profiles.id'], ),
    sa.ForeignKeyConstraint(['verified_by'], ['user_profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('markets',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('market_type', sa.String(length=20), nullable=True),
    sa.Column('creator_clerk_id', sa.String(length=255), nullable=False),
    sa.Column('creator_profile_id', sa.UUID(), nullable=True),
    sa.Column('creator_fee_percent', sa.DECIMAL(precision=5, scale=2), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('resolution_source', sa.Text(), nullable=True),
    sa.Column('oracle_type', sa.String(length=20), nullable=True),
    sa.Column('opens_at', sa.DateTime(), nullable=True),
    sa.Column('closes_at', sa.DateTime(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.Column('resolution_value', sa.String(length=255), nullable=True),
    sa.Column('total_volume', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('participant_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['creator_profile_id'], ['user_profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('wallets',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('clerk_user_id', sa.String(length=255), nullable=False),
    sa.Column('user_profile_id', sa.UUID(), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('balance', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('available_balance', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('locked_balance', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('wallet_address', sa.String(length=255), nullable=True),
    sa.Column('wallet_type', sa.String(length=20), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_profile_id'], ['user_profiles.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('wallet_address')
    )
    op.create_index('ix_wallets_user_currency', 'wallets', ['clerk_user_id', 'currency'], unique=True)
    op.create_table('game_players',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('session_id', sa.UUID(), nullable=True),
    sa.Column('clerk_user_id', sa.String(length=255), nullable=False),
    sa.Column('user_profile_id', sa.UUID(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('score', sa.Integer(), nullable=True),
    sa.Column('payout', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('joined_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['session_id'], ['game_sessions.id'], ),
    sa.ForeignKeyConstraint(['user_profile_id'], ['user_profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_game_players_session_user', 'game_players', ['session_id', 'clerk_user_id'], unique=True)
    op.create_table('market_outcomes',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('market_id', sa.UUID(), nullable=True),
    sa.Column('outcome_text', sa.String(length=255), nullable=True),
    sa.Column('outcome_value', sa.String(length=100), nullable=True),
    sa.Column('current_odds', sa.DECIMAL(precision=10, scale=4), nullable=True),
    sa.Column('total_backed', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('is_winner', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['market_id'], ['markets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('transactions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('clerk_user_id', sa.String(length=255), nullable=False),
    sa.Column('user_profile_id', sa.UUID(), nullable=True),
    sa.Column('wallet_id', sa.UUID(), nullable=True),
    sa.Column('type', sa.String(length=20), nullable=True),
    sa.Column('amount', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('fee', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('reference_id', sa.String(length=100), nullable=True),
    sa.Column('reference_type', sa.String(length=50), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('payment_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('stripe_payment_intent_id', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_profile_id'], ['user_profiles.id'], ),
    sa.ForeignKeyConstraint(['wallet_id'], ['wallets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('market_positions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('market_id', sa.UUID(), nullable=True),
    sa.Column('outcome_id', sa.UUID(), nullable=True),
    sa.Column('clerk_user_id', sa.String(length=255), nullable=False),
    sa.Column('user_profile_id', sa.UUID(), nullable=True),
    sa.Column('position_type', sa.String(length=10), nullable=True),
    sa.Column('stake', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('odds', sa.DECIMAL(precision=10, scale=4), nullable=True),
    sa.Column('potential_payout', sa.DECIMAL(precision=20, scale=8), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('settled_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['market_id'], ['markets.id'], ),
    sa.ForeignKeyConstraint(['outcome_id'], ['market_outcomes.id'], ),
    sa.ForeignKeyConstraint(['user_profile_id'], ['user_profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('market_positions')
    op.drop_table('transactions')
    op.drop_table('market_outcomes')
    op.drop_index('ix_game_players_session_user', table_name='game_players')
    op.drop_table('game_players')
    op.drop_index('ix_wallets_user_currency', table_name='wallets')
    op.drop_table('wallets')
    op.drop_table('markets')
    op.drop_table('kyc_documents')
    op.drop_table('game_sessions')
    op.drop_index(op.f('ix_user_profiles_clerk_user_id'), table_name='user_profiles')
    op.drop_table('user_profiles')
    op.drop_table('games')
    # ### end Alembic commands ###
