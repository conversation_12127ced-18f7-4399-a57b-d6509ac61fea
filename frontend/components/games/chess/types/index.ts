/**
 * Chess Game Types and Interfaces
 * All chess-specific type definitions
 */

export interface ChessGameConfig {
  // Basic Game Settings
  timeControl: 'Bullet' | 'Blitz' | 'Rapid' | 'Classical' | 'Custom';
  customTimeLimit?: number;
  customIncrement?: number;
  wagerAmount: number;
  isRated: boolean;
  playerColor: 'white' | 'black' | 'random';

  // Player & Privacy Settings
  inviteType: 'open' | 'specific' | 'friends';
  specificPlayerId?: string;
  isPrivate: boolean;
  requirePassword: boolean;
  gamePassword?: string;

  // Advanced Settings
  variant: 'standard' | 'chess960' | 'king-of-the-hill' | 'three-check';
  allowSpectators: boolean;
  allowChat: boolean;
  autoStart: boolean;

  // Tournament Settings
  isTournament: boolean;
  tournamentRounds?: number;
}

export interface ChessGameState {
  sessionId: string;
  boardFen: string;
  pgn: string;
  moveHistory: ChessMove[];
  whitePlayer: string;
  blackPlayer: string;
  whiteTime: number;
  blackTime: number;
  turn: 'white' | 'black';
  moveNumber: number;
  gameStarted: boolean;
  gameEnded: boolean;
  winner?: string;
  gameResult?: string;
  inCheck: boolean;
  legalMoves: string[];
  stakeAmount: number;
  currency: string;
  spectatorPool: number;
  gameType: string;
  createdAt: string;
  startedAt?: string;
  endedAt?: string;
}

export interface ChessMove {
  moveUci: string;
  playerId: string;
  timestamp: string;
  moveNumber: number;
  fenAfter: string;
  timeUsed: number;
}

export interface ChessPlayer {
  id: string;
  username: string;
  displayName?: string;
  avatarUrl?: string;
  rating?: number;
  level?: number;
  isOnline: boolean;
  isFriend?: boolean;
}

export interface ChessSpectatorBet {
  spectatorId: string;
  amount: number;
  prediction: 'white_wins' | 'black_wins' | 'draw';
  timestamp: string;
  odds: Record<string, number>;
}

export interface ChessTimeControl {
  baseTime: number; // in seconds
  increment: number; // in seconds
  whiteTime: number;
  blackTime: number;
}

export interface ChessWebSocketMessage {
  type: 'chess_move' | 'chess_resign' | 'chess_draw_offer' | 'chess_draw_accept' | 'chat_message' | 'spectator_bet' | 'ping';
  data?: any;
  move?: string;
  message?: string;
  amount?: number;
  prediction?: string;
  currency?: string;
}

export interface ChessGameResponse {
  sessionId: string;
  status: string;
  gameConfig: ChessGameConfig;
  gameState: any;
  websocketUrl: string;
  spectatorWebsocketUrl?: string;
  joinUrl?: string;
}

export interface ChessJoinResponse {
  status: string;
  gameStatus: string;
  yourColor: 'white' | 'black';
  opponentId: string;
  gameState: ChessGameState;
  websocketUrl: string;
}

// Chess variants enum
export enum ChessVariant {
  STANDARD = 'standard',
  CHESS960 = 'chess960',
  KING_OF_THE_HILL = 'king-of-the-hill',
  THREE_CHECK = 'three-check'
}

// Time control types
export enum TimeControlType {
  BULLET = 'Bullet',
  BLITZ = 'Blitz',
  RAPID = 'Rapid',
  CLASSICAL = 'Classical',
  CUSTOM = 'Custom'
}

// Game status enum
export enum ChessGameStatus {
  WAITING_FOR_PLAYER = 'waiting_for_player',
  READY_TO_START = 'ready_to_start',
  STARTED = 'started',
  ENDED = 'ended',
  CANCELLED = 'cancelled'
}

// Chess piece types
export type ChessPiece = 'p' | 'n' | 'b' | 'r' | 'q' | 'k' | 'P' | 'N' | 'B' | 'R' | 'Q' | 'K' | null;

export type ChessSquare =
  | 'a1' | 'b1' | 'c1' | 'd1' | 'e1' | 'f1' | 'g1' | 'h1'
  | 'a2' | 'b2' | 'c2' | 'd2' | 'e2' | 'f2' | 'g2' | 'h2'
  | 'a3' | 'b3' | 'c3' | 'd3' | 'e3' | 'f3' | 'g3' | 'h3'
  | 'a4' | 'b4' | 'c4' | 'd4' | 'e4' | 'f4' | 'g4' | 'h4'
  | 'a5' | 'b5' | 'c5' | 'd5' | 'e5' | 'f5' | 'g5' | 'h5'
  | 'a6' | 'b6' | 'c6' | 'd6' | 'e6' | 'f6' | 'g6' | 'h6'
  | 'a7' | 'b7' | 'c7' | 'd7' | 'e7' | 'f7' | 'g7' | 'h7'
  | 'a8' | 'b8' | 'c8' | 'd8' | 'e8' | 'f8' | 'g8' | 'h8';

export interface ChessBoardState {
  [square: string]: ChessPiece;
}