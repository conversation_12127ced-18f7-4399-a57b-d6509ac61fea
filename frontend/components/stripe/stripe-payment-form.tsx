"use client"

import { useState } from 'react'
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface StripePaymentFormProps {
  amount: number
  currency: string
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function StripePaymentForm({
  amount,
  currency,
  onSuccess,
  onError
}: StripePaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setIsLoading(true)

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/wallet?payment=success`,
        },
        redirect: 'if_required',
      })

      if (error) {
        if (error.type === 'card_error' || error.type === 'validation_error') {
          toast({
            title: "Payment Failed",
            description: error.message,
            variant: "destructive"
          })
          onError?.(error.message || 'Payment failed')
        } else {
          toast({
            title: "Payment Error",
            description: "An unexpected error occurred.",
            variant: "destructive"
          })
          onError?.('An unexpected error occurred')
        }
      } else {
        toast({
          title: "Payment Successful",
          description: `Successfully deposited ${amount} ${currency}`,
        })
        onSuccess?.()
      }
    } catch (err) {
      const message = 'Payment processing failed'
      toast({
        title: "Payment Error",
        description: message,
        variant: "destructive"
      })
      onError?.(message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Details</CardTitle>
        <div className="text-sm text-muted-foreground">
          Amount: <span className="font-semibold">{amount} {currency}</span>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <PaymentElement />

          <Button
            type="submit"
            disabled={!stripe || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              `Pay ${amount} ${currency}`
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}