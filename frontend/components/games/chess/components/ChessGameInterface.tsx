'use client';

import React, { useState, useEffect, useRef } from 'react';
import ChessBoard from './ChessBoard';
import { ChessGameState, ChessWebSocketMessage, ChessSquare } from '../types';

interface ChessGameInterfaceProps {
  gameState: ChessGameState;
  websocketUrl: string;
  playerId: string;
}

const ChessGameInterface: React.FC<ChessGameInterfaceProps> = ({
  gameState: initialGameState,
  websocketUrl,
  playerId
}) => {
  const [gameState, setGameState] = useState<ChessGameState>(initialGameState);
  const [isConnected, setIsConnected] = useState(false);
  const [chatMessages, setChatMessages] = useState<string[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [spectatorBet, setSpectatorBet] = useState({ amount: '', prediction: 'white_wins' });
  const wsRef = useRef<WebSocket | null>(null);

  const playerColor = gameState.whitePlayer === playerId ? 'white' : 'black';
  const isPlayerTurn = gameState.turn === playerColor && !gameState.gameEnded;

  useEffect(() => {
    if (!websocketUrl) return;

    const ws = new WebSocket(websocketUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      setIsConnected(true);
      console.log('Connected to chess game WebSocket');
    };

    ws.onmessage = (event) => {
      try {
        const message: ChessWebSocketMessage = JSON.parse(event.data);
        handleWebSocketMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      setIsConnected(false);
      console.log('Disconnected from chess game WebSocket');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [websocketUrl]);

  const handleWebSocketMessage = (message: ChessWebSocketMessage) => {
    switch (message.type) {
      case 'chess_move':
        if (message.data) {
          setGameState(prev => ({
            ...prev,
            ...message.data
          }));
        }
        break;

      case 'chat_message':
        if (message.message) {
          setChatMessages(prev => [...prev, message.message!]);
        }
        break;

      case 'chess_resign':
        setGameState(prev => ({
          ...prev,
          gameEnded: true,
          winner: message.data?.winner,
          gameResult: message.data?.result || 'Resignation'
        }));
        break;

      case 'ping':
        // Respond to ping to keep connection alive
        sendWebSocketMessage({ type: 'ping' });
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  };

  const sendWebSocketMessage = (message: ChessWebSocketMessage) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    }
  };

  const handleMove = (move: { from: ChessSquare; to: ChessSquare; promotion?: string }) => {
    if (!isPlayerTurn || !isConnected) return;

    const moveUci = `${move.from}${move.to}${move.promotion || ''}`;
    sendWebSocketMessage({
      type: 'chess_move',
      move: moveUci
    });
  };

  const handleResign = () => {
    if (confirm('Are you sure you want to resign?')) {
      sendWebSocketMessage({ type: 'chess_resign' });
    }
  };

  const handleDrawOffer = () => {
    sendWebSocketMessage({ type: 'chess_draw_offer' });
  };

  const handleChatSend = () => {
    if (chatInput.trim()) {
      sendWebSocketMessage({
        type: 'chat_message',
        message: chatInput.trim()
      });
      setChatInput('');
    }
  };

  const handleSpectatorBet = () => {
    const amount = parseFloat(spectatorBet.amount);
    if (amount > 0) {
      sendWebSocketMessage({
        type: 'spectator_bet',
        amount: amount,
        prediction: spectatorBet.prediction,
        currency: gameState.currency
      });
      setSpectatorBet({ amount: '', prediction: 'white_wins' });
    }
  };

  const isSpectator = playerId !== gameState.whitePlayer && playerId !== gameState.blackPlayer;

  return (
    <div className="flex flex-col lg:flex-row gap-6 p-6 max-w-7xl mx-auto">
      {/* Main Game Area */}
      <div className="flex-1">
        <div className="bg-white rounded-lg shadow-lg p-6">
          {/* Connection Status */}
          <div className="mb-4 flex items-center justify-between">
            <div className={`flex items-center gap-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              {isConnected ? 'Connected' : 'Disconnected'}
            </div>

            {gameState.stakeAmount > 0 && (
              <div className="text-lg font-bold text-green-600">
                Stake: {gameState.stakeAmount} {gameState.currency}
              </div>
            )}
          </div>

          {/* Players Info */}
          <div className="mb-4 flex justify-between items-center">
            <div className="text-center">
              <div className="font-semibold">{gameState.whitePlayer}</div>
              <div className="text-sm text-gray-500">White</div>
              <div className="text-lg font-mono">
                {Math.floor(gameState.whiteTime / 60)}:{String(gameState.whiteTime % 60).padStart(2, '0')}
              </div>
            </div>

            <div className="text-center text-gray-500">VS</div>

            <div className="text-center">
              <div className="font-semibold">{gameState.blackPlayer}</div>
              <div className="text-sm text-gray-500">Black</div>
              <div className="text-lg font-mono">
                {Math.floor(gameState.blackTime / 60)}:{String(gameState.blackTime % 60).padStart(2, '0')}
              </div>
            </div>
          </div>

          {/* Chess Board */}
          <ChessBoard
            gameState={gameState}
            isPlayerTurn={isPlayerTurn}
            playerColor={playerColor}
            onMove={handleMove}
            disabled={!isConnected || isSpectator}
          />

          {/* Game Controls */}
          {!isSpectator && !gameState.gameEnded && (
            <div className="mt-6 flex gap-4 justify-center">
              <button
                onClick={handleResign}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                disabled={!isConnected}
              >
                Resign
              </button>
              <button
                onClick={handleDrawOffer}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                disabled={!isConnected}
              >
                Offer Draw
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Sidebar */}
      <div className="w-full lg:w-80 space-y-6">
        {/* Spectator Betting */}
        {isSpectator && !gameState.gameEnded && (
          <div className="bg-white rounded-lg shadow-lg p-4">
            <h3 className="text-lg font-semibold mb-4">Place Bet</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-1">Amount ({gameState.currency})</label>
                <input
                  type="number"
                  value={spectatorBet.amount}
                  onChange={(e) => setSpectatorBet(prev => ({ ...prev, amount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter amount"
                  min="1"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Prediction</label>
                <select
                  value={spectatorBet.prediction}
                  onChange={(e) => setSpectatorBet(prev => ({ ...prev, prediction: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="white_wins">White Wins</option>
                  <option value="black_wins">Black Wins</option>
                  <option value="draw">Draw</option>
                </select>
              </div>
              <button
                onClick={handleSpectatorBet}
                disabled={!spectatorBet.amount || !isConnected}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:bg-gray-400"
              >
                Place Bet
              </button>
            </div>

            {gameState.spectatorPool > 0 && (
              <div className="mt-4 text-center text-sm text-gray-600">
                Spectator Pool: {gameState.spectatorPool} {gameState.currency}
              </div>
            )}
          </div>
        )}

        {/* Move History */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h3 className="text-lg font-semibold mb-4">Move History</h3>
          <div className="max-h-64 overflow-y-auto">
            {gameState.moveHistory.length === 0 ? (
              <div className="text-gray-500 text-sm">No moves yet</div>
            ) : (
              <div className="space-y-1">
                {gameState.moveHistory.map((move, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="font-mono">{Math.floor(index / 2) + 1}.</span>
                    <span className="font-mono">{move.moveUci}</span>
                    <span className="text-gray-500">{Math.floor(move.timeUsed / 60)}:{String(move.timeUsed % 60).padStart(2, '0')}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Chat */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h3 className="text-lg font-semibold mb-4">Chat</h3>

          <div className="h-48 overflow-y-auto border border-gray-200 rounded p-2 mb-3">
            {chatMessages.length === 0 ? (
              <div className="text-gray-500 text-sm">No messages yet</div>
            ) : (
              chatMessages.map((message, index) => (
                <div key={index} className="text-sm mb-1">{message}</div>
              ))
            )}
          </div>

          <div className="flex gap-2">
            <input
              type="text"
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleChatSend()}
              className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Type a message..."
              disabled={!isConnected}
            />
            <button
              onClick={handleChatSend}
              disabled={!chatInput.trim() || !isConnected}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:bg-gray-400"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChessGameInterface;