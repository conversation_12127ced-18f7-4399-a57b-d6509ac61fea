"use client"

import { <PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UserButton } from "@clerk/nextjs"
import Image from "next/image"
import Link from "next/link"

export function TopNavbar() {
  return (
    <header className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 h-16">
      <div className="flex items-center justify-between h-full px-4 md:pl-72">
        <div className="flex items-center gap-4">
          {/* Logo - visible on mobile, hidden on desktop where sidebar logo shows */}
          <Link href="/dashboard" className="md:hidden flex items-center gap-2">
            <Image
              src="/images/betbet.logo.png"
              alt="BetBet Logo"
              width={32}
              height={32}
              className="rounded-lg"
            />
            <span className="font-bold text-lg">BetBet</span>
          </Link>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-md mx-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="search"
              placeholder="Search games, markets, experts..."
              className="pl-10 bg-gray-50 border-gray-200 focus:bg-white"
            />
          </div>
        </div>

        {/* Right Actions */}
        <div className="flex items-center gap-3">
          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              3
            </span>
          </Button>

          {/* Clerk UserButton for session management */}
          <UserButton
            afterSignOutUrl="/"
            appearance={{
              elements: {
                avatarBox: "h-9 w-9",
                userButtonPopoverCard: "shadow-lg",
                userButtonPopoverActionButton: "hover:bg-gray-100"
              }
            }}
          />
        </div>
      </div>
    </header>
  )
}
