"""
BetBet Social Forum Service
Handles communities, discussion threads, groups, and social interactions
"""

import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from decimal import Decimal

from fastapi import FastAP<PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func, desc, or_
from sqlalchemy.orm import selectinload, joinedload
from pydantic import BaseModel, Field

import sys
sys.path.append('../..')
from shared.database import (
    init_all_databases, close_all_databases, get_db,
    get_redis_client
)
from shared.clerk_auth_simple import (
    ClerkUser, get_current_user, get_current_user_optional,
    require_kyc_level
)
from shared.models import (
    UserProfile, Community, CommunityMember, Thread, ThreadReply,
    Group, GroupMember
)

# Pydantic models for request/response
class CommunityCreate(BaseModel):
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    type: str = "public"  # 'public', 'private', 'paid'
    membership_fee: Optional[Decimal] = None

class CommunityResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    category: Optional[str]
    type: str
    membership_fee: Decimal
    owner_id: str
    member_count: int
    created_at: datetime

class ThreadCreate(BaseModel):
    community_id: str
    title: str
    content: str
    thread_type: str = "discussion"
    has_paywall: bool = False
    paywall_amount: Optional[Decimal] = None

class ThreadResponse(BaseModel):
    id: str
    community_id: str
    author_id: str
    title: str
    content: str
    thread_type: str
    is_pinned: bool
    is_locked: bool
    upvotes: int
    downvotes: int
    reply_count: int
    view_count: int
    has_paywall: bool
    paywall_amount: Optional[Decimal]
    created_at: datetime
    updated_at: datetime

class ThreadReplyCreate(BaseModel):
    thread_id: str
    content: str
    parent_reply_id: Optional[str] = None

class ThreadReplyResponse(BaseModel):
    id: str
    thread_id: str
    parent_reply_id: Optional[str]
    author_id: str
    content: str
    upvotes: int
    downvotes: int
    is_deleted: bool
    created_at: datetime
    updated_at: datetime

class GroupCreate(BaseModel):
    name: str
    description: Optional[str] = None
    type: str = "public"
    max_members: int = 100

class GroupResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    type: str
    owner_id: str
    max_members: int
    member_count: int
    created_at: datetime

# Lifespan manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Starting Social Forum Service...")
    await init_all_databases()
    yield
    print("Shutting down Social Forum Service...")
    await close_all_databases()

# Create FastAPI app
app = FastAPI(
    title="BetBet Social Forum Service",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Helper functions
async def check_community_membership(db: AsyncSession, community_id: str, user_id: str) -> Optional[CommunityMember]:
    """Check if user is member of community"""
    result = await db.execute(
        select(CommunityMember).where(
            and_(
                CommunityMember.community_id == community_id,
                CommunityMember.user_id == user_id
            )
        )
    )
    return result.scalar_one_or_none()

async def increment_thread_views(db: AsyncSession, thread_id: str):
    """Increment thread view count"""
    await db.execute(
        update(Thread)
        .where(Thread.id == thread_id)
        .values(view_count=Thread.view_count + 1)
    )
    await db.commit()

# Community Endpoints
@app.get("/api/v1/communities", response_model=List[CommunityResponse])
async def list_communities(
    category: Optional[str] = None,
    community_type: Optional[str] = None,
    page: int = 1,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """List communities with optional filtering"""

    query = select(Community)

    # Apply filters
    if category:
        query = query.where(Community.category == category)
    if community_type:
        query = query.where(Community.type == community_type)

    # Add pagination
    offset = (page - 1) * limit
    query = query.order_by(desc(Community.created_at)).offset(offset).limit(limit)

    result = await db.execute(query)
    communities = result.scalars().all()

    return [
        CommunityResponse(
            id=str(community.id),
            name=community.name,
            description=community.description,
            category=community.category,
            type=community.type,
            membership_fee=community.membership_fee,
            owner_id=community.owner_id,
            member_count=community.member_count,
            created_at=community.created_at
        )
        for community in communities
    ]

@app.post("/api/v1/communities/create", response_model=CommunityResponse)
async def create_community(
    community_data: CommunityCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new community"""

    # Check if community name already exists
    result = await db.execute(
        select(Community).where(Community.name == community_data.name)
    )
    existing = result.scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=400, detail="Community name already exists")

    # Create community
    community = Community(
        name=community_data.name,
        description=community_data.description,
        category=community_data.category,
        type=community_data.type,
        membership_fee=community_data.membership_fee or Decimal('0'),
        owner_id=current_user.user_id,
        member_count=1  # Owner is automatically a member
    )

    db.add(community)
    await db.commit()
    await db.refresh(community)

    # Add owner as first member with admin role
    owner_membership = CommunityMember(
        community_id=community.id,
        user_id=current_user.user_id,
        role='owner'
    )
    db.add(owner_membership)
    await db.commit()

    return CommunityResponse(
        id=str(community.id),
        name=community.name,
        description=community.description,
        category=community.category,
        type=community.type,
        membership_fee=community.membership_fee,
        owner_id=community.owner_id,
        member_count=community.member_count,
        created_at=community.created_at
    )

@app.post("/api/v1/communities/{community_id}/join")
async def join_community(
    community_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Join a community"""

    # Check if community exists
    community = await db.get(Community, community_id)
    if not community:
        raise HTTPException(status_code=404, detail="Community not found")

    # Check if already a member
    existing_membership = await check_community_membership(db, community_id, current_user.user_id)
    if existing_membership:
        raise HTTPException(status_code=400, detail="Already a member")

    # Check if private community requires invitation (simplified for now)
    if community.type == 'private':
        raise HTTPException(status_code=403, detail="Private community requires invitation")

    # Add membership
    membership = CommunityMember(
        community_id=community_id,
        user_id=current_user.user_id,
        role='member'
    )
    db.add(membership)

    # Increment member count
    community.member_count += 1

    await db.commit()

    return {
        "message": "Successfully joined community",
        "community_id": community_id,
        "role": "member"
    }

@app.get("/api/v1/communities/{community_id}/members")
async def get_community_members(
    community_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get community members"""

    # Check if user is member of community
    membership = await check_community_membership(db, community_id, current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Get all members
    result = await db.execute(
        select(CommunityMember).where(CommunityMember.community_id == community_id)
        .order_by(desc(CommunityMember.joined_at))
    )
    members = result.scalars().all()

    return [
        {
            "user_id": member.user_id,
            "role": member.role,
            "joined_at": member.joined_at
        }
        for member in members
    ]

# Thread Endpoints
@app.get("/api/v1/communities/{community_id}/threads", response_model=List[ThreadResponse])
async def get_community_threads(
    community_id: str,
    page: int = 1,
    limit: int = 20,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get threads in a community"""

    # Check if user is member of community
    membership = await check_community_membership(db, community_id, current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Get threads
    offset = (page - 1) * limit
    query = (
        select(Thread)
        .where(Thread.community_id == community_id)
        .order_by(desc(Thread.is_pinned), desc(Thread.created_at))
        .offset(offset)
        .limit(limit)
    )

    result = await db.execute(query)
    threads = result.scalars().all()

    return [
        ThreadResponse(
            id=str(thread.id),
            community_id=str(thread.community_id),
            author_id=thread.author_id,
            title=thread.title,
            content=thread.content,
            thread_type=thread.thread_type,
            is_pinned=thread.is_pinned,
            is_locked=thread.is_locked,
            upvotes=thread.upvotes,
            downvotes=thread.downvotes,
            reply_count=thread.reply_count,
            view_count=thread.view_count,
            has_paywall=thread.has_paywall,
            paywall_amount=thread.paywall_amount,
            created_at=thread.created_at,
            updated_at=thread.updated_at
        )
        for thread in threads
    ]

@app.post("/api/v1/threads/create", response_model=ThreadResponse)
async def create_thread(
    thread_data: ThreadCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new thread"""

    # Check if user is member of community
    membership = await check_community_membership(db, thread_data.community_id, current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Create thread
    thread = Thread(
        community_id=thread_data.community_id,
        author_id=current_user.user_id,
        title=thread_data.title,
        content=thread_data.content,
        thread_type=thread_data.thread_type,
        has_paywall=thread_data.has_paywall,
        paywall_amount=thread_data.paywall_amount
    )

    db.add(thread)
    await db.commit()
    await db.refresh(thread)

    return ThreadResponse(
        id=str(thread.id),
        community_id=str(thread.community_id),
        author_id=thread.author_id,
        title=thread.title,
        content=thread.content,
        thread_type=thread.thread_type,
        is_pinned=thread.is_pinned,
        is_locked=thread.is_locked,
        upvotes=thread.upvotes,
        downvotes=thread.downvotes,
        reply_count=thread.reply_count,
        view_count=thread.view_count,
        has_paywall=thread.has_paywall,
        paywall_amount=thread.paywall_amount,
        created_at=thread.created_at,
        updated_at=thread.updated_at
    )

@app.get("/api/v1/threads/{thread_id}", response_model=ThreadResponse)
async def get_thread(
    thread_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get specific thread"""

    thread = await db.get(Thread, thread_id)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")

    # Check if user is member of community
    membership = await check_community_membership(db, str(thread.community_id), current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Increment view count
    await increment_thread_views(db, thread_id)

    return ThreadResponse(
        id=str(thread.id),
        community_id=str(thread.community_id),
        author_id=thread.author_id,
        title=thread.title,
        content=thread.content,
        thread_type=thread.thread_type,
        is_pinned=thread.is_pinned,
        is_locked=thread.is_locked,
        upvotes=thread.upvotes,
        downvotes=thread.downvotes,
        reply_count=thread.reply_count,
        view_count=thread.view_count + 1,  # Include the increment
        has_paywall=thread.has_paywall,
        paywall_amount=thread.paywall_amount,
        created_at=thread.created_at,
        updated_at=thread.updated_at
    )

@app.get("/api/v1/threads/{thread_id}/replies", response_model=List[ThreadReplyResponse])
async def get_thread_replies(
    thread_id: str,
    page: int = 1,
    limit: int = 50,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get replies to a thread"""

    # Get thread to check community membership
    thread = await db.get(Thread, thread_id)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")

    # Check if user is member of community
    membership = await check_community_membership(db, str(thread.community_id), current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Get replies
    offset = (page - 1) * limit
    query = (
        select(ThreadReply)
        .where(and_(ThreadReply.thread_id == thread_id, ThreadReply.is_deleted == False))
        .order_by(ThreadReply.created_at)
        .offset(offset)
        .limit(limit)
    )

    result = await db.execute(query)
    replies = result.scalars().all()

    return [
        ThreadReplyResponse(
            id=str(reply.id),
            thread_id=str(reply.thread_id),
            parent_reply_id=str(reply.parent_reply_id) if reply.parent_reply_id else None,
            author_id=reply.author_id,
            content=reply.content,
            upvotes=reply.upvotes,
            downvotes=reply.downvotes,
            is_deleted=reply.is_deleted,
            created_at=reply.created_at,
            updated_at=reply.updated_at
        )
        for reply in replies
    ]

@app.post("/api/v1/threads/reply", response_model=ThreadReplyResponse)
async def create_thread_reply(
    reply_data: ThreadReplyCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Reply to a thread"""

    # Get thread to check community membership
    thread = await db.get(Thread, reply_data.thread_id)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")

    # Check if thread is locked
    if thread.is_locked:
        raise HTTPException(status_code=403, detail="Thread is locked")

    # Check if user is member of community
    membership = await check_community_membership(db, str(thread.community_id), current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Create reply
    reply = ThreadReply(
        thread_id=reply_data.thread_id,
        parent_reply_id=reply_data.parent_reply_id,
        author_id=current_user.user_id,
        content=reply_data.content
    )

    db.add(reply)

    # Increment thread reply count
    thread.reply_count += 1

    await db.commit()
    await db.refresh(reply)

    return ThreadReplyResponse(
        id=str(reply.id),
        thread_id=str(reply.thread_id),
        parent_reply_id=str(reply.parent_reply_id) if reply.parent_reply_id else None,
        author_id=reply.author_id,
        content=reply.content,
        upvotes=reply.upvotes,
        downvotes=reply.downvotes,
        is_deleted=reply.is_deleted,
        created_at=reply.created_at,
        updated_at=reply.updated_at
    )

# Group Endpoints (simplified betting groups)
@app.post("/api/v1/groups/create", response_model=GroupResponse)
async def create_group(
    group_data: GroupCreate,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new betting group"""

    # Create group
    group = Group(
        name=group_data.name,
        description=group_data.description,
        type=group_data.type,
        owner_id=current_user.user_id,
        max_members=group_data.max_members,
        member_count=1  # Owner is automatically a member
    )

    db.add(group)
    await db.commit()
    await db.refresh(group)

    # Add owner as first member
    owner_membership = GroupMember(
        group_id=group.id,
        user_id=current_user.user_id,
        role='owner'
    )
    db.add(owner_membership)
    await db.commit()

    return GroupResponse(
        id=str(group.id),
        name=group.name,
        description=group.description,
        type=group.type,
        owner_id=group.owner_id,
        max_members=group.max_members,
        member_count=group.member_count,
        created_at=group.created_at
    )

@app.get("/api/v1/groups", response_model=List[GroupResponse])
async def list_groups(
    page: int = 1,
    limit: int = 20,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List available groups"""

    offset = (page - 1) * limit
    query = (
        select(Group)
        .where(Group.type == 'public')  # Only show public groups
        .order_by(desc(Group.created_at))
        .offset(offset)
        .limit(limit)
    )

    result = await db.execute(query)
    groups = result.scalars().all()

    return [
        GroupResponse(
            id=str(group.id),
            name=group.name,
            description=group.description,
            type=group.type,
            owner_id=group.owner_id,
            max_members=group.max_members,
            member_count=group.member_count,
            created_at=group.created_at
        )
        for group in groups
    ]

@app.post("/api/v1/groups/{group_id}/join")
async def join_group(
    group_id: str,
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Join a betting group"""

    # Check if group exists
    group = await db.get(Group, group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")

    # Check if already a member
    result = await db.execute(
        select(GroupMember).where(
            and_(GroupMember.group_id == group_id, GroupMember.user_id == current_user.user_id)
        )
    )
    existing_membership = result.scalar_one_or_none()
    if existing_membership:
        raise HTTPException(status_code=400, detail="Already a member")

    # Check if group is full
    if group.member_count >= group.max_members:
        raise HTTPException(status_code=400, detail="Group is full")

    # Add membership
    membership = GroupMember(
        group_id=group_id,
        user_id=current_user.user_id,
        role='member'
    )
    db.add(membership)

    # Increment member count
    group.member_count += 1

    await db.commit()

    return {
        "message": "Successfully joined group",
        "group_id": group_id,
        "role": "member"
    }

# Vote endpoints (simplified)
@app.post("/api/v1/threads/{thread_id}/vote")
async def vote_on_thread(
    thread_id: str,
    vote_type: str,  # 'upvote' or 'downvote'
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Vote on a thread"""

    if vote_type not in ['upvote', 'downvote']:
        raise HTTPException(status_code=400, detail="Invalid vote type")

    thread = await db.get(Thread, thread_id)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")

    # Check if user is member of community
    membership = await check_community_membership(db, str(thread.community_id), current_user.user_id)
    if not membership:
        raise HTTPException(status_code=403, detail="Not a member of this community")

    # Update vote count (simplified - no duplicate vote prevention)
    if vote_type == 'upvote':
        thread.upvotes += 1
    else:
        thread.downvotes += 1

    await db.commit()

    return {
        "message": f"Thread {vote_type}d successfully",
        "upvotes": thread.upvotes,
        "downvotes": thread.downvotes
    }

# Test endpoints
@app.post("/api/v1/test/create-sample-communities")
async def create_sample_communities(
    current_user: ClerkUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create sample communities for testing"""

    sample_communities = [
        {"name": "Sports Betting", "description": "Discuss sports betting strategies", "category": "sports"},
        {"name": "Crypto Predictions", "description": "Share cryptocurrency predictions", "category": "crypto"},
        {"name": "General Discussion", "description": "General betting and gaming discussions", "category": "general"}
    ]

    communities_created = []

    for community_data in sample_communities:
        # Check if exists
        result = await db.execute(
            select(Community).where(Community.name == community_data["name"])
        )
        existing = result.scalar_one_or_none()
        if existing:
            continue

        # Create community
        community = Community(
            name=community_data["name"],
            description=community_data["description"],
            category=community_data["category"],
            owner_id=current_user.user_id,
            member_count=1
        )
        db.add(community)
        await db.commit()
        await db.refresh(community)

        # Add owner membership
        membership = CommunityMember(
            community_id=community.id,
            user_id=current_user.user_id,
            role='owner'
        )
        db.add(membership)

        communities_created.append({
            "id": str(community.id),
            "name": community.name,
            "category": community.category
        })

    await db.commit()

    return {
        "message": "Sample communities created",
        "communities": communities_created
    }

# Health check
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "social_forum"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)