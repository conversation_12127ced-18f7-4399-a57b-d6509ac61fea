"""
BetBet Chess Game Engine
Real-time chess implementation with wagering support
"""

import chess
import chess.engine
import chess.pgn
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from enum import Enum


class ChessGameType(Enum):
    BULLET = "bullet"  # < 3 minutes
    BLITZ = "blitz"    # 3-10 minutes
    RAPID = "rapid"    # 10-60 minutes
    CLASSICAL = "classical"  # > 60 minutes


class ChessTimeControl:
    def __init__(self, base_minutes: int, increment_seconds: int = 0):
        self.base_time = base_minutes * 60  # Convert to seconds
        self.increment = increment_seconds
        self.white_time = self.base_time
        self.black_time = self.base_time

    def get_game_type(self) -> ChessGameType:
        """Determine game type based on time control"""
        total_time = self.base_time + (self.increment * 40)  # Estimate 40 moves

        if total_time < 180:  # < 3 minutes
            return ChessGameType.BULLET
        elif total_time < 600:  # < 10 minutes
            return ChessGameType.BLITZ
        elif total_time < 3600:  # < 60 minutes
            return ChessGameType.RAPID
        else:
            return ChessGameType.CLASSICAL


class ChessGame:
    def __init__(
        self,
        session_id: str,
        white_player_id: str,
        black_player_id: str,
        time_control: ChessTimeControl,
        stake_amount: float = 0.0,
        currency: str = "USD"
    ):
        self.session_id = session_id
        self.white_player_id = white_player_id
        self.black_player_id = black_player_id
        self.board = chess.Board()
        self.time_control = time_control
        self.stake_amount = stake_amount
        self.currency = currency

        # Game state
        self.game_started = False
        self.game_ended = False
        self.winner = None
        self.game_result = None
        self.resignation = False
        self.timeout = False

        # Move tracking
        self.move_history = []
        self.last_move_time = None
        self.move_start_time = None

        # Spectator wagering
        self.spectator_bets = {}
        self.total_spectator_pool = 0.0

        # Game metadata
        self.created_at = datetime.utcnow()
        self.started_at = None
        self.ended_at = None

    def start_game(self) -> Dict[str, Any]:
        """Start the chess game"""
        if self.game_started:
            return {"error": "Game already started"}

        self.game_started = True
        self.started_at = datetime.utcnow()
        self.move_start_time = self.started_at

        return {
            "status": "game_started",
            "board_fen": self.board.fen(),
            "white_player": self.white_player_id,
            "black_player": self.black_player_id,
            "white_time": self.time_control.white_time,
            "black_time": self.time_control.black_time,
            "turn": "white",
            "move_number": 1
        }

    def make_move(self, player_id: str, move_uci: str) -> Dict[str, Any]:
        """Make a chess move"""
        if not self.game_started or self.game_ended:
            return {"error": "Game not active"}

        # Verify it's the player's turn
        current_turn = "white" if self.board.turn == chess.WHITE else "black"
        expected_player = self.white_player_id if current_turn == "white" else self.black_player_id

        if player_id != expected_player:
            return {"error": "Not your turn"}

        # Update time
        now = datetime.utcnow()
        if self.move_start_time:
            time_used = (now - self.move_start_time).total_seconds()

            if current_turn == "white":
                self.time_control.white_time -= time_used
                self.time_control.white_time += self.time_control.increment
            else:
                self.time_control.black_time -= time_used
                self.time_control.black_time += self.time_control.increment

            # Check for time forfeit
            if (current_turn == "white" and self.time_control.white_time <= 0) or \
               (current_turn == "black" and self.time_control.black_time <= 0):
                return self._end_game_by_timeout(current_turn)

        # Validate and make move
        try:
            move = chess.Move.from_uci(move_uci)
            if move not in self.board.legal_moves:
                return {"error": "Illegal move"}

            self.board.push(move)
            self.move_history.append({
                "move_uci": move_uci,
                "player_id": player_id,
                "timestamp": now,
                "move_number": len(self.move_history) + 1,
                "fen_after": self.board.fen(),
                "time_used": time_used if self.move_start_time else 0
            })

            self.last_move_time = now
            self.move_start_time = now

            # Check for game end conditions
            result = self._check_game_end()
            if result:
                return result

            return {
                "status": "move_made",
                "move": move_uci,
                "board_fen": self.board.fen(),
                "turn": "black" if current_turn == "white" else "white",
                "white_time": max(0, self.time_control.white_time),
                "black_time": max(0, self.time_control.black_time),
                "move_number": len(self.move_history),
                "legal_moves": [move.uci() for move in self.board.legal_moves],
                "in_check": self.board.is_check(),
                "last_move": self.move_history[-1]
            }

        except (ValueError, AssertionError):
            return {"error": "Invalid move format"}

    def resign(self, player_id: str) -> Dict[str, Any]:
        """Player resigns"""
        if not self.game_started or self.game_ended:
            return {"error": "Game not active"}

        self.resignation = True

        if player_id == self.white_player_id:
            self.winner = self.black_player_id
            self.game_result = "0-1"
        else:
            self.winner = self.white_player_id
            self.game_result = "1-0"

        return self._end_game("resignation")

    def offer_draw(self, player_id: str) -> Dict[str, Any]:
        """Offer a draw"""
        return {
            "status": "draw_offered",
            "offered_by": player_id,
            "message": f"Player {player_id} offers a draw"
        }

    def accept_draw(self) -> Dict[str, Any]:
        """Accept draw offer"""
        self.game_result = "1/2-1/2"
        self.winner = None
        return self._end_game("draw_agreement")

    def place_spectator_bet(
        self,
        spectator_id: str,
        bet_amount: float,
        prediction: str  # "white_wins", "black_wins", "draw"
    ) -> Dict[str, Any]:
        """Place a spectator bet on game outcome"""
        if self.game_ended:
            return {"error": "Game already ended"}

        if spectator_id not in self.spectator_bets:
            self.spectator_bets[spectator_id] = []

        bet = {
            "amount": bet_amount,
            "prediction": prediction,
            "timestamp": datetime.utcnow(),
            "odds": self._calculate_current_odds()
        }

        self.spectator_bets[spectator_id].append(bet)
        self.total_spectator_pool += bet_amount

        return {
            "status": "bet_placed",
            "bet": bet,
            "total_pool": self.total_spectator_pool
        }

    def get_game_state(self) -> Dict[str, Any]:
        """Get current game state for spectators and players"""
        return {
            "session_id": self.session_id,
            "board_fen": self.board.fen(),
            "pgn": self._get_pgn(),
            "move_history": self.move_history,
            "white_player": self.white_player_id,
            "black_player": self.black_player_id,
            "white_time": max(0, self.time_control.white_time),
            "black_time": max(0, self.time_control.black_time),
            "turn": "white" if self.board.turn == chess.WHITE else "black",
            "move_number": len(self.move_history) + 1,
            "game_started": self.game_started,
            "game_ended": self.game_ended,
            "winner": self.winner,
            "game_result": self.game_result,
            "in_check": self.board.is_check(),
            "legal_moves": [move.uci() for move in self.board.legal_moves] if not self.game_ended else [],
            "stake_amount": self.stake_amount,
            "currency": self.currency,
            "spectator_pool": self.total_spectator_pool,
            "game_type": self.time_control.get_game_type().value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None
        }

    def _check_game_end(self) -> Optional[Dict[str, Any]]:
        """Check if the game has ended"""
        if self.board.is_checkmate():
            winner = self.white_player_id if self.board.turn == chess.BLACK else self.black_player_id
            result = "1-0" if winner == self.white_player_id else "0-1"
            self.winner = winner
            self.game_result = result
            return self._end_game("checkmate")

        elif self.board.is_stalemate():
            self.game_result = "1/2-1/2"
            return self._end_game("stalemate")

        elif self.board.is_insufficient_material():
            self.game_result = "1/2-1/2"
            return self._end_game("insufficient_material")

        elif self.board.is_seventyfive_moves():
            self.game_result = "1/2-1/2"
            return self._end_game("75_move_rule")

        elif self.board.is_fivefold_repetition():
            self.game_result = "1/2-1/2"
            return self._end_game("repetition")

        return None

    def _end_game_by_timeout(self, player_on_time: str) -> Dict[str, Any]:
        """End game due to timeout"""
        self.timeout = True

        if player_on_time == "white":
            self.winner = self.black_player_id
            self.game_result = "0-1"
        else:
            self.winner = self.white_player_id
            self.game_result = "1-0"

        return self._end_game("timeout")

    def _end_game(self, reason: str) -> Dict[str, Any]:
        """End the game and calculate payouts"""
        self.game_ended = True
        self.ended_at = datetime.utcnow()

        # Calculate main game payout
        main_payout = self._calculate_main_payout()

        # Calculate spectator payouts
        spectator_payouts = self._calculate_spectator_payouts()

        game_state = self.get_game_state()
        game_state.update({
            "status": "game_ended",
            "end_reason": reason,
            "main_payout": main_payout,
            "spectator_payouts": spectator_payouts,
            "final_position": self.board.fen()
        })

        return game_state

    def _calculate_main_payout(self) -> Dict[str, float]:
        """Calculate main game payout between players"""
        if not self.stake_amount or self.stake_amount <= 0:
            return {}

        total_pot = self.stake_amount * 2  # Both players' stakes

        if self.game_result == "1-0":  # White wins
            return {
                self.white_player_id: total_pot,
                self.black_player_id: 0
            }
        elif self.game_result == "0-1":  # Black wins
            return {
                self.white_player_id: 0,
                self.black_player_id: total_pot
            }
        else:  # Draw
            return {
                self.white_player_id: self.stake_amount,
                self.black_player_id: self.stake_amount
            }

    def _calculate_spectator_payouts(self) -> Dict[str, float]:
        """Calculate spectator bet payouts"""
        if not self.spectator_bets or self.total_spectator_pool <= 0:
            return {}

        payouts = {}

        # Determine winning prediction
        if self.game_result == "1-0":
            winning_prediction = "white_wins"
        elif self.game_result == "0-1":
            winning_prediction = "black_wins"
        else:
            winning_prediction = "draw"

        # Calculate total winning bets
        total_winning_amount = 0
        for spectator_id, bets in self.spectator_bets.items():
            for bet in bets:
                if bet["prediction"] == winning_prediction:
                    total_winning_amount += bet["amount"]

        if total_winning_amount == 0:
            # No winners, distribute evenly or keep as house edge
            return {}

        # Distribute winnings proportionally
        for spectator_id, bets in self.spectator_bets.items():
            spectator_winnings = 0

            for bet in bets:
                if bet["prediction"] == winning_prediction:
                    # Proportional share of total pool
                    share = bet["amount"] / total_winning_amount
                    spectator_winnings += self.total_spectator_pool * share

            if spectator_winnings > 0:
                payouts[spectator_id] = spectator_winnings

        return payouts

    def _calculate_current_odds(self) -> Dict[str, float]:
        """Calculate current betting odds based on position"""
        # Simplified odds calculation
        # In production, this would use a chess engine for evaluation

        if self.board.is_check():
            if self.board.turn == chess.WHITE:
                return {"white_wins": 2.5, "black_wins": 1.4, "draw": 4.0}
            else:
                return {"white_wins": 1.4, "black_wins": 2.5, "draw": 4.0}

        # Default odds for balanced position
        return {"white_wins": 1.9, "black_wins": 1.9, "draw": 3.5}

    def _get_pgn(self) -> str:
        """Get PGN representation of the game"""
        game = chess.pgn.Game()
        game.headers["White"] = self.white_player_id
        game.headers["Black"] = self.black_player_id
        game.headers["Date"] = self.created_at.strftime("%Y.%m.%d")
        game.headers["Site"] = "BetBet Platform"
        game.headers["Result"] = self.game_result or "*"

        node = game
        board = chess.Board()

        for move_data in self.move_history:
            move = chess.Move.from_uci(move_data["move_uci"])
            node = node.add_variation(move)
            board.push(move)

        return str(game)


class ChessGameManager:
    """Manages multiple chess games"""

    def __init__(self):
        self.active_games: Dict[str, ChessGame] = {}

    def create_game(
        self,
        session_id: str,
        white_player_id: str,
        black_player_id: str,
        time_control: ChessTimeControl,
        stake_amount: float = 0.0,
        currency: str = "USD"
    ) -> ChessGame:
        """Create a new chess game"""
        game = ChessGame(
            session_id=session_id,
            white_player_id=white_player_id,
            black_player_id=black_player_id,
            time_control=time_control,
            stake_amount=stake_amount,
            currency=currency
        )

        self.active_games[session_id] = game
        return game

    def get_game(self, session_id: str) -> Optional[ChessGame]:
        """Get a chess game by session ID"""
        return self.active_games.get(session_id)

    def remove_game(self, session_id: str):
        """Remove a completed game"""
        if session_id in self.active_games:
            del self.active_games[session_id]

    def get_active_games(self) -> List[ChessGame]:
        """Get all active games"""
        return [game for game in self.active_games.values() if not game.game_ended]


# Global chess game manager
chess_manager = ChessGameManager()