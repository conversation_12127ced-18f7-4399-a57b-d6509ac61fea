import React, { useState, useEffect } from 'react';
import {
  X,
  Clock,
  DollarSign,
  Users,
  Crown,
  Settings,
  Play,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Globe,
  UserPlus,
  Lock,
  Search,
  Trophy,
  Zap,
  Shield,
  AlertCircle,
  MessageSquare
} from 'lucide-react';

export interface ChessGameConfig {
  // Basic Game Settings
  timeControl: 'Bullet' | 'Blitz' | 'Rapid' | 'Classical' | 'Custom';
  customTimeLimit?: number;
  customIncrement?: number;
  wagerAmount: number;
  isRated: boolean;
  playerColor: 'white' | 'black' | 'random';

  // Player & Privacy Settings
  inviteType: 'open' | 'specific' | 'friends';
  specificPlayerId?: string;
  isPrivate: boolean;
  requirePassword: boolean;
  gamePassword?: string;

  // Advanced Settings
  variant: 'standard' | 'chess960' | 'king-of-the-hill' | 'three-check';
  allowSpectators: boolean;
  allowChat: boolean;
  autoStart: boolean;

  // Tournament Settings
  isTournament: boolean;
  tournamentRounds?: number;
}

interface User {
  id: string;
  username: string;
  avatar_url?: string;
  level?: number;
  is_friend?: boolean;
  is_online?: boolean;
  status?: string;
}

interface ChessGameCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateGame: (config: ChessGameConfig) => Promise<void>;
  isCreating?: boolean;
  error?: string | null;
}

export const ChessGameCreationModal: React.FC<ChessGameCreationModalProps> = ({
  isOpen,
  onClose,
  onCreateGame,
  isCreating = false,
  error = null
}) => {
  // Wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  // Basic Game Settings (Step 1)
  const [timeControl, setTimeControl] = useState<'Bullet' | 'Blitz' | 'Rapid' | 'Classical' | 'Custom'>('Rapid');
  const [customTimeLimit, setCustomTimeLimit] = useState(10);
  const [customIncrement, setCustomIncrement] = useState(5);
  const [wagerAmount, setWagerAmount] = useState(10);
  const [isRated, setIsRated] = useState(true);
  const [playerColor, setPlayerColor] = useState<'white' | 'black' | 'random'>('random');

  // Player & Privacy Settings (Step 2)
  const [inviteType, setInviteType] = useState<'open' | 'specific' | 'friends'>('open');
  const [specificPlayerId, setSpecificPlayerId] = useState<string>('');
  const [playerSearchQuery, setPlayerSearchQuery] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [requirePassword, setRequirePassword] = useState(false);
  const [gamePassword, setGamePassword] = useState('');

  // Advanced Settings (Step 3)
  const [variant, setVariant] = useState<'standard' | 'chess960' | 'king-of-the-hill' | 'three-check'>('standard');
  const [allowSpectators, setAllowSpectators] = useState(true);
  const [allowChat, setAllowChat] = useState(true);
  const [autoStart, setAutoStart] = useState(true);

  // Tournament Settings (Step 4)
  const [isTournament, setIsTournament] = useState(false);
  const [tournamentRounds, setTournamentRounds] = useState(3);

  // UI State
  const [selectedPlayer, setSelectedPlayer] = useState<string | null>(null);
  const [friends, setFriends] = useState<User[]>([]);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isLoadingFriends, setIsLoadingFriends] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const TIME_CONTROLS = {
    'Bullet': { time: '1+1', description: 'Lightning fast games' },
    'Blitz': { time: '3+2', description: 'Quick tactical games' },
    'Rapid': { time: '10+10', description: 'Balanced gameplay' },
    'Classical': { time: '30+30', description: 'Deep strategic games' },
    'Custom': { time: 'Custom', description: 'Set your own time' }
  };

  const PREDEFINED_WAGERS = [5, 10, 25, 50, 100, 250, 500, 1000];

  const CHESS_VARIANTS = {
    'standard': { name: 'Standard Chess', description: 'Classic chess rules' },
    'chess960': { name: 'Chess960', description: 'Random starting positions' },
    'king-of-the-hill': { name: 'King of the Hill', description: 'Control the center to win' },
    'three-check': { name: 'Three-Check', description: 'Give check 3 times to win' }
  };

  // Mock friends data for demonstration
  useEffect(() => {
    if (inviteType === 'friends' && friends.length === 0) {
      // Simulate loading friends
      setIsLoadingFriends(true);
      setTimeout(() => {
        setFriends([
          { id: '1', username: 'john_doe', level: 15, is_online: true, is_friend: true },
          { id: '2', username: 'chess_master', level: 20, is_online: false, is_friend: true },
          { id: '3', username: 'pawn_king', level: 8, is_online: true, is_friend: true }
        ]);
        setIsLoadingFriends(false);
      }, 1000);
    }
  }, [inviteType]);

  // Handle search with debouncing
  useEffect(() => {
    if (playerSearchQuery.length >= 3) {
      setIsSearching(true);
      const timeoutId = setTimeout(() => {
        // Mock search results
        setSearchResults([
          { id: '4', username: 'player_' + playerSearchQuery.toLowerCase(), level: 12, is_online: true, is_friend: false },
          { id: '5', username: playerSearchQuery.toLowerCase() + '_chess', level: 16, is_online: false, is_friend: false }
        ]);
        setIsSearching(false);
      }, 500);
      
      return () => clearTimeout(timeoutId);
    } else {
      setSearchResults([]);
    }
  }, [playerSearchQuery]);

  const validateCurrentStep = (): boolean => {
    const errors: Record<string, string> = {};
    
    switch (currentStep) {
      case 1:
        if (wagerAmount < 5 || wagerAmount > 1000) {
          errors.wagerAmount = 'Wager amount must be between $5-$1000';
        }
        if (timeControl === 'Custom') {
          if (customTimeLimit < 1 || customTimeLimit > 180) {
            errors.customTimeLimit = 'Time limit must be between 1-180 minutes';
          }
          if (customIncrement < 0 || customIncrement > 60) {
            errors.customIncrement = 'Increment must be between 0-60 seconds';
          }
        }
        break;
      case 2:
        if (inviteType === 'specific' && !specificPlayerId) {
          errors.specificPlayer = 'Please select a player to invite';
        }
        if (requirePassword && (!gamePassword || gamePassword.length < 4)) {
          errors.gamePassword = 'Password must be at least 4 characters';
        }
        break;
      case 4:
        if (isTournament && (tournamentRounds < 2 || tournamentRounds > 10)) {
          errors.tournamentRounds = 'Tournament must have 2-10 rounds';
        }
        break;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep() && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCreateGame = async () => {
    if (!validateCurrentStep()) return;

    const config: ChessGameConfig = {
      // Basic Game Settings
      timeControl,
      customTimeLimit: timeControl === 'Custom' ? customTimeLimit : undefined,
      customIncrement: timeControl === 'Custom' ? customIncrement : undefined,
      wagerAmount,
      isRated,
      playerColor,

      // Player & Privacy Settings
      inviteType,
      specificPlayerId: inviteType === 'specific' ? specificPlayerId : undefined,
      isPrivate,
      requirePassword,
      gamePassword: requirePassword ? gamePassword : undefined,

      // Advanced Settings
      variant,
      allowSpectators,
      allowChat,
      autoStart,

      // Tournament Settings
      isTournament,
      tournamentRounds: isTournament ? tournamentRounds : undefined,
    };

    try {
      await onCreateGame(config);
    } catch (error) {
      // Error handled by parent
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'Game Settings';
      case 2: return 'Players & Privacy';
      case 3: return 'Advanced Options';
      case 4: return 'Tournament Setup';
      default: return 'Setup';
    }
  };

  const getStepIcon = () => {
    switch (currentStep) {
      case 1: return <Settings className="h-5 w-5" />;
      case 2: return <Users className="h-5 w-5" />;
      case 3: return <Zap className="h-5 w-5" />;
      case 4: return <Trophy className="h-5 w-5" />;
      default: return <Settings className="h-5 w-5" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-slate-900 border border-slate-700 rounded-xl w-full max-w-3xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg">
              <Crown className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Create Chess Game</h2>
              <p className="text-sm text-slate-400">Step {currentStep} of {totalSteps}: {getStepTitle()}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-slate-400 hover:text-white transition-colors"
            disabled={isCreating}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-3 bg-slate-800/50">
          <div className="flex items-center space-x-2">
            {Array.from({ length: totalSteps }, (_, i) => (
              <React.Fragment key={i}>
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium ${
                  i + 1 === currentStep 
                    ? 'bg-blue-600 text-white' 
                    : i + 1 < currentStep 
                    ? 'bg-green-600 text-white' 
                    : 'bg-slate-700 text-slate-400'
                }`}>
                  {i + 1 < currentStep ? '✓' : i + 1}
                </div>
                {i < totalSteps - 1 && (
                  <div className={`flex-1 h-1 rounded ${
                    i + 1 < currentStep ? 'bg-green-600' : 'bg-slate-700'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-280px)] overflow-y-auto">
          {/* Step 1: Basic Game Settings */}
          {currentStep === 1 && (
            <div className="space-y-6">
              {/* Time Control */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-blue-400" />
                  <h3 className="text-lg font-semibold text-white">Time Control</h3>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  {Object.entries(TIME_CONTROLS).map(([key, value]) => (
                    <div
                      key={key}
                      onClick={() => setTimeControl(key as any)}
                      className={`p-3 rounded-lg border cursor-pointer transition-all ${
                        timeControl === key
                          ? 'border-blue-500 bg-blue-500/20'
                          : 'border-slate-700 bg-slate-800 hover:border-slate-600'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-white">{key}</span>
                        {timeControl === key && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                      <div className="text-sm text-slate-400 mt-1">{value.time}</div>
                      <div className="text-xs text-slate-500 mt-1">{value.description}</div>
                    </div>
                  ))}
                </div>

                {timeControl === 'Custom' && (
                  <div className="grid grid-cols-2 gap-4 p-4 bg-slate-800 rounded-lg">
                    <div>
                      <label className="text-sm text-slate-400">Time (minutes)</label>
                      <input
                        type="number"
                        value={customTimeLimit}
                        onChange={(e) => setCustomTimeLimit(Number(e.target.value))}
                        className="w-full mt-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="1" max="180"
                      />
                      {validationErrors.customTimeLimit && (
                        <div className="text-red-400 text-xs mt-1">{validationErrors.customTimeLimit}</div>
                      )}
                    </div>
                    <div>
                      <label className="text-sm text-slate-400">Increment (seconds)</label>
                      <input
                        type="number"
                        value={customIncrement}
                        onChange={(e) => setCustomIncrement(Number(e.target.value))}
                        className="w-full mt-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0" max="60"
                      />
                      {validationErrors.customIncrement && (
                        <div className="text-red-400 text-xs mt-1">{validationErrors.customIncrement}</div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Wager Amount */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-green-400" />
                  <h3 className="text-lg font-semibold text-white">Wager Amount</h3>
                </div>
                
                <div className="grid grid-cols-4 gap-2">
                  {PREDEFINED_WAGERS.map((amount) => (
                    <button
                      key={amount}
                      onClick={() => setWagerAmount(amount)}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        wagerAmount === amount
                          ? 'bg-green-600 text-white'
                          : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
                      }`}
                    >
                      ${amount}
                    </button>
                  ))}
                </div>

                <div>
                  <label className="text-sm text-slate-400">Custom Amount</label>
                  <div className="relative mt-1">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-500" />
                    <input
                      type="number"
                      value={wagerAmount}
                      onChange={(e) => setWagerAmount(Number(e.target.value))}
                      className="w-full pl-10 pr-3 py-2 bg-slate-800 border border-slate-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                      min="5" max="1000" step="5"
                      placeholder="Enter custom amount"
                    />
                  </div>
                  {validationErrors.wagerAmount && (
                    <div className="text-red-400 text-xs mt-1">{validationErrors.wagerAmount}</div>
                  )}
                </div>
              </div>

              {/* Player Color & Rating */}
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Crown className="h-5 w-5 text-yellow-400" />
                    <h3 className="text-lg font-semibold text-white">Color</h3>
                  </div>
                  <div className="space-y-2">
                    {[
                      { value: 'white', label: 'White', icon: '♔' },
                      { value: 'black', label: 'Black', icon: '♚' },
                      { value: 'random', label: 'Random', icon: '🎲' }
                    ].map((option) => (
                      <label key={option.value} className="flex items-center space-x-3 cursor-pointer">
                        <input
                          type="radio"
                          name="color"
                          value={option.value}
                          checked={playerColor === option.value}
                          onChange={(e) => setPlayerColor(e.target.value as any)}
                          className="w-4 h-4 text-blue-600"
                        />
                        <span className="text-lg">{option.icon}</span>
                        <span className="text-white">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Trophy className="h-5 w-5 text-purple-400" />
                    <h3 className="text-lg font-semibold text-white">Rating</h3>
                  </div>
                  <div className="space-y-3">
                    <label className="flex items-center justify-between p-3 bg-slate-800 rounded-lg cursor-pointer">
                      <div>
                        <div className="text-sm font-medium text-white">Rated Game</div>
                        <div className="text-xs text-slate-400">Affects your rating</div>
                      </div>
                      <input
                        type="checkbox"
                        checked={isRated}
                        onChange={(e) => setIsRated(e.target.checked)}
                        className="w-4 h-4 text-blue-600"
                      />
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Players & Privacy */}
          {currentStep === 2 && (
            <div className="space-y-6">
              {/* Invite Type */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-purple-400" />
                  <h3 className="text-lg font-semibold text-white">Player Invitation</h3>
                </div>

                <div className="space-y-3">
                  {[
                    { value: 'open', label: 'Open to Anyone', icon: Globe, color: 'text-green-500', desc: 'Any player can join' },
                    { value: 'specific', label: 'Invite Specific Player', icon: UserPlus, color: 'text-blue-500', desc: 'Send direct invitation' },
                    { value: 'friends', label: 'Friends Only', icon: Users, color: 'text-yellow-500', desc: 'Only friends can join' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center space-x-3 p-3 bg-slate-800 rounded-lg cursor-pointer hover:bg-slate-700">
                      <input
                        type="radio"
                        name="inviteType"
                        value={option.value}
                        checked={inviteType === option.value}
                        onChange={(e) => setInviteType(e.target.value as any)}
                        className="w-4 h-4 text-blue-600"
                      />
                      <option.icon className={`h-4 w-4 ${option.color}`} />
                      <div className="flex-1">
                        <div className="text-white font-medium">{option.label}</div>
                        <div className="text-xs text-slate-400">{option.desc}</div>
                      </div>
                    </label>
                  ))}
                </div>

                {/* Player Search */}
                {inviteType === 'specific' && (
                  <div className="p-4 bg-slate-800 rounded-lg space-y-3">
                    <label className="text-sm font-medium text-white">Search for Player</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-500" />
                      <input
                        placeholder="Enter username..."
                        value={playerSearchQuery}
                        onChange={(e) => setPlayerSearchQuery(e.target.value)}
                        className="w-full pl-10 pr-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    
                    {playerSearchQuery && (
                      <div className="max-h-32 overflow-y-auto space-y-2">
                        {isSearching ? (
                          <div className="text-center py-4 text-slate-400">Searching...</div>
                        ) : searchResults.length === 0 ? (
                          <div className="text-center py-4 text-slate-400">
                            {playerSearchQuery.length < 3 ? 'Type at least 3 characters' : 'No players found'}
                          </div>
                        ) : (
                          searchResults.map((player) => (
                            <div
                              key={player.id}
                              onClick={() => {
                                setSpecificPlayerId(player.id);
                                setSelectedPlayer(player.id);
                                setPlayerSearchQuery(player.username);
                              }}
                              className={`p-2 rounded cursor-pointer transition-colors ${
                                selectedPlayer === player.id ? 'bg-blue-600' : 'bg-slate-700 hover:bg-slate-600'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <span className="text-white">{player.username}</span>
                                <div className="flex space-x-1">
                                  <span className="text-xs bg-yellow-600 px-2 py-1 rounded">Lvl {player.level}</span>
                                  <span className={`text-xs px-2 py-1 rounded ${player.is_online ? 'bg-green-600' : 'bg-slate-600'}`}>
                                    {player.is_online ? 'Online' : 'Offline'}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    )}
                    {validationErrors.specificPlayer && (
                      <div className="text-red-400 text-xs">{validationErrors.specificPlayer}</div>
                    )}
                  </div>
                )}

                {/* Friends List */}
                {inviteType === 'friends' && (
                  <div className="p-4 bg-slate-800 rounded-lg space-y-3">
                    <label className="text-sm font-medium text-white">Your Friends</label>
                    {isLoadingFriends ? (
                      <div className="text-center py-4 text-slate-400">Loading friends...</div>
                    ) : friends.length === 0 ? (
                      <div className="text-center py-4 text-slate-400">No friends found</div>
                    ) : (
                      <div className="max-h-32 overflow-y-auto space-y-2">
                        {friends.map((friend) => (
                          <div
                            key={friend.id}
                            onClick={() => {
                              setSpecificPlayerId(friend.id);
                              setSelectedPlayer(friend.id);
                            }}
                            className={`p-2 rounded cursor-pointer transition-colors ${
                              selectedPlayer === friend.id ? 'bg-blue-600' : 'bg-slate-700 hover:bg-slate-600'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white">{friend.username}</span>
                              <div className="flex space-x-1">
                                <span className="text-xs bg-yellow-600 px-2 py-1 rounded">Lvl {friend.level}</span>
                                <span className={`text-xs px-2 py-1 rounded ${friend.is_online ? 'bg-green-600' : 'bg-slate-600'}`}>
                                  {friend.is_online ? 'Online' : 'Offline'}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Privacy Settings */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Lock className="h-5 w-5 text-red-400" />
                  <h3 className="text-lg font-semibold text-white">Privacy Settings</h3>
                </div>

                <div className="space-y-3">
                  <label className="flex items-center justify-between p-3 bg-slate-800 rounded-lg cursor-pointer">
                    <div>
                      <div className="text-sm font-medium text-white">Private Game</div>
                      <div className="text-xs text-slate-400">Hide from public listings</div>
                    </div>
                    <input
                      type="checkbox"
                      checked={isPrivate}
                      onChange={(e) => setIsPrivate(e.target.checked)}
                      className="w-4 h-4 text-red-600"
                    />
                  </label>

                  <label className="flex items-center justify-between p-3 bg-slate-800 rounded-lg cursor-pointer">
                    <div>
                      <div className="text-sm font-medium text-white">Require Password</div>
                      <div className="text-xs text-slate-400">Players need password to join</div>
                    </div>
                    <input
                      type="checkbox"
                      checked={requirePassword}
                      onChange={(e) => setRequirePassword(e.target.checked)}
                      className="w-4 h-4 text-orange-600"
                    />
                  </label>

                  {requirePassword && (
                    <div>
                      <label className="text-sm text-slate-400">Game Password</label>
                      <input
                        type="password"
                        value={gamePassword}
                        onChange={(e) => setGamePassword(e.target.value)}
                        className="w-full mt-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                        placeholder="Enter game password"
                      />
                      {validationErrors.gamePassword && (
                        <div className="text-red-400 text-xs mt-1">{validationErrors.gamePassword}</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Advanced Options */}
          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Chess Variant */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Crown className="h-5 w-5 text-purple-400" />
                  <h3 className="text-lg font-semibold text-white">Chess Variant</h3>
                </div>

                <div className="space-y-3">
                  {Object.entries(CHESS_VARIANTS).map(([key, value]) => (
                    <label key={key} className="flex items-center space-x-3 p-3 bg-slate-800 rounded-lg cursor-pointer hover:bg-slate-700">
                      <input
                        type="radio"
                        name="variant"
                        value={key}
                        checked={variant === key}
                        onChange={(e) => setVariant(e.target.value as any)}
                        className="w-4 h-4 text-purple-600"
                      />
                      <div className="flex-1">
                        <div className="text-white font-medium">{value.name}</div>
                        <div className="text-xs text-slate-400">{value.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Game Features */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-blue-400" />
                  <h3 className="text-lg font-semibold text-white">Game Features</h3>
                </div>

                <div className="space-y-3">
                  <label className="flex items-center justify-between p-3 bg-slate-800 rounded-lg cursor-pointer">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-blue-400" />
                      <div>
                        <div className="text-sm font-medium text-white">Allow Spectators</div>
                        <div className="text-xs text-slate-400">Let others watch your game</div>
                      </div>
                    </div>
                    <input
                      type="checkbox"
                      checked={allowSpectators}
                      onChange={(e) => setAllowSpectators(e.target.checked)}
                      className="w-4 h-4 text-blue-600"
                    />
                  </label>

                  <label className="flex items-center justify-between p-3 bg-slate-800 rounded-lg cursor-pointer">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-4 w-4 text-green-400" />
                      <div>
                        <div className="text-sm font-medium text-white">Enable Chat</div>
                        <div className="text-xs text-slate-400">Allow in-game messaging</div>
                      </div>
                    </div>
                    <input
                      type="checkbox"
                      checked={allowChat}
                      onChange={(e) => setAllowChat(e.target.checked)}
                      className="w-4 h-4 text-green-600"
                    />
                  </label>

                  <label className="flex items-center justify-between p-3 bg-slate-800 rounded-lg cursor-pointer">
                    <div className="flex items-center space-x-2">
                      <Zap className="h-4 w-4 text-yellow-400" />
                      <div>
                        <div className="text-sm font-medium text-white">Auto Start</div>
                        <div className="text-xs text-slate-400">Start immediately when both players join</div>
                      </div>
                    </div>
                    <input
                      type="checkbox"
                      checked={autoStart}
                      onChange={(e) => setAutoStart(e.target.checked)}
                      className="w-4 h-4 text-yellow-600"
                    />
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Tournament Setup */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Trophy className="h-5 w-5 text-yellow-400" />
                  <h3 className="text-lg font-semibold text-white">Tournament Mode</h3>
                </div>

                <label className="flex items-center justify-between p-4 bg-slate-800 rounded-lg cursor-pointer">
                  <div>
                    <div className="text-sm font-medium text-white">Enable Tournament Mode</div>
                    <div className="text-xs text-slate-400">Create a multi-round tournament</div>
                  </div>
                  <input
                    type="checkbox"
                    checked={isTournament}
                    onChange={(e) => setIsTournament(e.target.checked)}
                    className="w-4 h-4 text-yellow-600"
                  />
                </label>

                {isTournament && (
                  <div className="space-y-4 p-4 bg-slate-800 rounded-lg">
                    <div>
                      <label className="text-sm font-medium text-white">Number of Rounds</label>
                      <select
                        value={tournamentRounds}
                        onChange={(e) => setTournamentRounds(Number(e.target.value))}
                        className="w-full mt-2 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      >
                        <option value={3}>3 Rounds</option>
                        <option value={5}>5 Rounds</option>
                        <option value={7}>7 Rounds</option>
                        <option value={10}>10 Rounds</option>
                      </select>
                      {validationErrors.tournamentRounds && (
                        <div className="text-red-400 text-xs mt-1">{validationErrors.tournamentRounds}</div>
                      )}
                    </div>

                    <div className="text-sm text-slate-400">
                      <p className="font-medium text-white mb-2">Tournament features:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Multiple rounds with elimination</li>
                        <li>Increased prize pool</li>
                        <li>Tournament leaderboard</li>
                        <li>Special tournament badges</li>
                      </ul>
                    </div>
                  </div>
                )}

                {!isTournament && (
                  <div className="text-center p-8 text-slate-400">
                    <Trophy className="h-12 w-12 mx-auto mb-4 text-slate-600" />
                    <p>Enable tournament mode to access advanced tournament features</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-900/30 border border-red-500/30 rounded-lg">
              <div className="flex items-center space-x-2 text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-slate-700">
          <div className="text-sm text-slate-400">
            Total Wager: <span className="text-green-400 font-bold">${wagerAmount}</span>
            {isTournament && <span className="ml-2">(Tournament: {tournamentRounds} rounds)</span>}
          </div>
          
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <button
                onClick={handlePrev}
                className="px-4 py-2 flex items-center space-x-2 text-slate-400 hover:text-white transition-colors"
                disabled={isCreating}
              >
                <ChevronLeft className="h-4 w-4" />
                <span>Previous</span>
              </button>
            )}
            
            <button
              onClick={onClose}
              className="px-4 py-2 text-slate-400 hover:text-white transition-colors"
              disabled={isCreating}
            >
              Cancel
            </button>
            
            {currentStep < totalSteps ? (
              <button
                onClick={handleNext}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <span>Next</span>
                <ChevronRight className="h-4 w-4" />
              </button>
            ) : (
              <button
                onClick={handleCreateGame}
                disabled={isCreating}
                className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-lg font-medium transition-colors flex items-center space-x-2 disabled:opacity-50"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4" />
                    <span>Create Game</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChessGameCreationModal;