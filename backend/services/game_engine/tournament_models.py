"""
Tournament Models for BetBet Gaming Platform
Supports various tournament formats and bracket management
"""

from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
import uuid

Base = declarative_base()

class TournamentStatus(enum.Enum):
    REGISTRATION = "registration"
    READY = "ready"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class TournamentFormat(enum.Enum):
    SINGLE_ELIMINATION = "single_elimination"
    DOUBLE_ELIMINATION = "double_elimination"
    ROUND_ROBIN = "round_robin"
    SWISS = "swiss"

class BracketMatchStatus(enum.Enum):
    PENDING = "pending"
    ACTIVE = "active"
    COMPLETED = "completed"
    FORFEIT = "forfeit"

class Tournament(Base):
    __tablename__ = "tournaments"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(Text)
    game_type = Column(String, nullable=False)  # chess, checkers, poker, etc.
    format = Column(SQLEnum(TournamentFormat), nullable=False)
    status = Column(SQLEnum(TournamentStatus), default=TournamentStatus.REGISTRATION)

    # Tournament Configuration
    max_participants = Column(Integer, nullable=False)
    entry_fee = Column(Float, default=0.0)
    prize_pool = Column(Float, default=0.0)
    currency = Column(String, default="USD")

    # Time Management
    registration_start = Column(DateTime, default=datetime.utcnow)
    registration_end = Column(DateTime, nullable=False)
    tournament_start = Column(DateTime, nullable=False)
    tournament_end = Column(DateTime)

    # Game Settings
    game_settings = Column(Text)  # JSON string for game-specific settings

    # Tournament Rules
    is_rated = Column(Boolean, default=True)
    allow_spectators = Column(Boolean, default=True)
    allow_chat = Column(Boolean, default=True)

    # Creator and Organization
    creator_id = Column(String, nullable=False)
    organizer_name = Column(String)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    participants = relationship("TournamentParticipant", back_populates="tournament")
    brackets = relationship("TournamentBracket", back_populates="tournament")
    matches = relationship("BracketMatch", back_populates="tournament")

class TournamentParticipant(Base):
    __tablename__ = "tournament_participants"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tournament_id = Column(String, ForeignKey("tournaments.id"), nullable=False)
    player_id = Column(String, nullable=False)
    player_name = Column(String, nullable=False)

    # Registration Info
    registered_at = Column(DateTime, default=datetime.utcnow)
    seed_number = Column(Integer)
    rating = Column(Integer)

    # Tournament Progress
    is_active = Column(Boolean, default=True)
    eliminated_at = Column(DateTime)
    final_position = Column(Integer)

    # Prize Info
    prize_amount = Column(Float, default=0.0)
    prize_currency = Column(String, default="USD")

    # Relationships
    tournament = relationship("Tournament", back_populates="participants")

class TournamentBracket(Base):
    __tablename__ = "tournament_brackets"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tournament_id = Column(String, ForeignKey("tournaments.id"), nullable=False)
    bracket_type = Column(String, nullable=False)  # main, losers, consolation
    round_number = Column(Integer, nullable=False)
    position = Column(Integer, nullable=False)

    # Bracket Structure
    parent_bracket_id = Column(String, ForeignKey("tournament_brackets.id"))
    left_child_bracket_id = Column(String, ForeignKey("tournament_brackets.id"))
    right_child_bracket_id = Column(String, ForeignKey("tournament_brackets.id"))

    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    tournament = relationship("Tournament", back_populates="brackets")
    matches = relationship("BracketMatch", back_populates="bracket")

class BracketMatch(Base):
    __tablename__ = "bracket_matches"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tournament_id = Column(String, ForeignKey("tournaments.id"), nullable=False)
    bracket_id = Column(String, ForeignKey("tournament_brackets.id"), nullable=False)
    game_session_id = Column(String)  # Link to actual game session

    # Match Info
    round_number = Column(Integer, nullable=False)
    match_number = Column(Integer, nullable=False)
    status = Column(SQLEnum(BracketMatchStatus), default=BracketMatchStatus.PENDING)

    # Participants
    player1_id = Column(String)
    player2_id = Column(String)
    player1_name = Column(String)
    player2_name = Column(String)
    player1_seed = Column(Integer)
    player2_seed = Column(Integer)

    # Results
    winner_id = Column(String)
    loser_id = Column(String)
    result = Column(String)  # Game-specific result format
    score = Column(String)   # Match score if applicable

    # Timing
    scheduled_start = Column(DateTime)
    actual_start = Column(DateTime)
    completed_at = Column(DateTime)

    # Game Settings
    game_settings = Column(Text)  # JSON string for match-specific settings

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tournament = relationship("Tournament", back_populates="matches")
    bracket = relationship("TournamentBracket", back_populates="matches")

class TournamentPrizeStructure(Base):
    __tablename__ = "tournament_prize_structures"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tournament_id = Column(String, ForeignKey("tournaments.id"), nullable=False)

    position = Column(Integer, nullable=False)  # 1st, 2nd, 3rd, etc.
    prize_amount = Column(Float, nullable=False)
    prize_percentage = Column(Float)  # Percentage of total prize pool
    currency = Column(String, default="USD")

    created_at = Column(DateTime, default=datetime.utcnow)