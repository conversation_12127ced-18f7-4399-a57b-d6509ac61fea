"use client"

import { useUser } from "@clerk/nextjs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  TrendingUp,
  Gamepad2,
  BarChart3,
  MessageSquare,
  Clock,
  Trophy,
  DollarSign,
  Users,
  ArrowUpRight,
  Calendar,
  Star,
  RefreshCw,
  Zap,
  Target,
  Heart,
  Sparkles,
} from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"

interface Activity {
  type: string
  title: string
  amount?: string
  time: string
  icon: any
  color: string
  category?: string
}

interface UserStats {
  portfolioValue: string
  activeBets: string
  winRate: string
  followers: string
  change: {
    portfolio: string
    bets: string
    winRate: string
    followers: string
  }
}

export default function DashboardPage() {
  const { user, isLoaded } = useUser()
  const [activities, setActivities] = useState<Activity[]>([])
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [recommendations, setRecommendations] = useState<any[]>([])
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Get user's first name or fallback to "there"
  const firstName = user?.firstName || user?.username || "there"

  // Get current time-based greeting
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Good morning"
    if (hour < 18) return "Good afternoon"
    return "Good evening"
  }

  // Simulate fetching personalized data based on user
  useEffect(() => {
    if (user) {
      // Simulate API call to fetch user's personalized feed
      fetchUserActivities()
      fetchUserStats()
      fetchRecommendations()
    }
  }, [user])

  const fetchUserActivities = () => {
    // Simulate personalized activities based on user's history
    const personalizedActivities: Activity[] = [
      {
        type: "bet",
        title: "Won bet on Lakers vs Warriors",
        amount: "+$125.50",
        time: "2 hours ago",
        icon: TrendingUp,
        color: "text-green-500",
        category: "sports"
      },
      {
        type: "game",
        title: "Chess match victory against GrandMaster_99",
        amount: "+$50.00",
        time: "4 hours ago",
        icon: Trophy,
        color: "text-yellow-500",
        category: "gaming"
      },
      {
        type: "market",
        title: "Your prediction market is trending",
        time: "6 hours ago",
        icon: Zap,
        color: "text-purple-500",
        category: "markets"
      },
      {
        type: "social",
        title: "5 new followers from your analysis post",
        time: "1 day ago",
        icon: Users,
        color: "text-blue-500",
        category: "social"
      },
      {
        type: "achievement",
        title: "Unlocked 'Winning Streak' badge",
        time: "2 days ago",
        icon: Sparkles,
        color: "text-pink-500",
        category: "achievement"
      }
    ]
    setActivities(personalizedActivities)
  }

  const fetchUserStats = () => {
    // Simulate fetching user's personal stats
    setUserStats({
      portfolioValue: "$2,847.50",
      activeBets: "7",
      winRate: "73%",
      followers: "1.2K",
      change: {
        portfolio: "+12.5%",
        bets: "+2",
        winRate: "+5%",
        followers: "+48"
      }
    })
  }

  const fetchRecommendations = () => {
    // Personalized recommendations based on user behavior
    const recs = [
      {
        title: "Chess Tournament Starting",
        description: "Based on your recent chess wins",
        prize: "$5,000 prize pool",
        icon: Gamepad2,
        link: "/gaming/chess/tournament"
      },
      {
        title: "NBA Finals Predictions Open",
        description: "You've been successful with NBA bets",
        odds: "Great odds available",
        icon: Target,
        link: "/betting/nba-finals"
      },
      {
        title: "Follow Expert Analyst Mike",
        description: "85% win rate this month",
        followers: "2.3K followers",
        icon: Star,
        link: "/profile/mike"
      }
    ]
    setRecommendations(recs)
  }

  const refreshFeed = () => {
    setIsRefreshing(true)
    setTimeout(() => {
      fetchUserActivities()
      fetchUserStats()
      setIsRefreshing(false)
    }, 1000)
  }

  const quickStats = userStats ? [
    { label: "Portfolio Value", value: userStats.portfolioValue, change: userStats.change.portfolio, icon: DollarSign },
    { label: "Active Bets", value: userStats.activeBets, change: userStats.change.bets, icon: TrendingUp },
    { label: "Win Rate", value: userStats.winRate, change: userStats.change.winRate, icon: Trophy },
    { label: "Followers", value: userStats.followers, change: userStats.change.followers, icon: Users },
  ] : []

  const platformNews = [
    {
      title: "New Crypto Payment Options",
      description: "We now support 15+ cryptocurrencies",
      time: "3 hours ago",
      category: "Update",
    },
    {
      title: "Weekly Tournament Series",
      description: "$50K prize pool across all games",
      time: "1 day ago",
      category: "Tournament",
    },
    {
      title: "AI Analysis Tools Enhanced",
      description: "Better prediction accuracy",
      time: "2 days ago",
      category: "Feature",
    },
  ]

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading your dashboard...</div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Personalized Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {getGreeting()}, {firstName}!
          </h1>
          <p className="text-muted-foreground">
            Here's your personalized dashboard for today
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshFeed}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh Feed
          </Button>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            {new Date().toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      {userStats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {quickStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.label}</CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-green-500 flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    {stat.change} from last month
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Personalized Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Recommended for You
            </CardTitle>
            <CardDescription>Based on your interests and activity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {recommendations.map((rec, index) => {
                const Icon = rec.icon
                return (
                  <Link key={index} href={rec.link}>
                    <div className="p-4 rounded-lg border hover:border-primary transition-colors cursor-pointer">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-lg bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium mb-1">{rec.title}</h4>
                          <p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
                          <Badge variant="secondary">
                            {rec.prize || rec.odds || rec.followers}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {/* Your Activity Feed */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Your Activity Feed
            </CardTitle>
            <CardDescription>
              Personalized based on your interests and history
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {activities.map((activity, index) => {
              const Icon = activity.icon
              return (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full bg-background ${activity.color}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="font-medium">{activity.title}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-sm text-muted-foreground">{activity.time}</p>
                        {activity.category && (
                          <Badge variant="outline" className="text-xs">
                            {activity.category}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  {activity.amount && (
                    <Badge variant="secondary" className="text-green-600 bg-green-50">
                      {activity.amount}
                    </Badge>
                  )}
                </div>
              )
            })}
            <Button variant="outline" className="w-full bg-transparent">
              View All Activity
            </Button>
          </CardContent>
        </Card>

        {/* Platform News */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Platform Updates
            </CardTitle>
            <CardDescription>Latest news and features</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {platformNews.map((news, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {news.category}
                  </Badge>
                  <span className="text-xs text-muted-foreground">{news.time}</span>
                </div>
                <h4 className="font-medium text-sm">{news.title}</h4>
                <p className="text-xs text-muted-foreground">{news.description}</p>
              </div>
            ))}
            <Button variant="outline" size="sm" className="w-full bg-transparent">
              View All Updates
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Jump into your favorite activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
            <Link href="/betting/create">
              <Button className="w-full justify-start bg-transparent" variant="outline">
                <BarChart3 className="h-4 w-4 mr-2" />
                Create Market
              </Button>
            </Link>
            <Link href="/gaming">
              <Button className="w-full justify-start bg-transparent" variant="outline">
                <Gamepad2 className="h-4 w-4 mr-2" />
                Join Game
              </Button>
            </Link>
            <Link href="/wallet">
              <Button className="w-full justify-start bg-transparent" variant="outline">
                <DollarSign className="h-4 w-4 mr-2" />
                Manage Wallet
              </Button>
            </Link>
            <Link href="/analysis">
              <Button className="w-full justify-start bg-transparent" variant="outline">
                <TrendingUp className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}