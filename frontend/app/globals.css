@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* #ffffff */
  --foreground: oklch(0.278 0.013 286.62); /* #374151 */
  --card: oklch(0.985 0.002 286.38); /* #f8fafc */
  --card-foreground: oklch(0.198 0.015 286.75); /* #1f2937 */
  --popover: oklch(1 0 0); /* #ffffff */
  --popover-foreground: oklch(0.278 0.013 286.62); /* #374151 */
  --primary: oklch(0.278 0.013 286.62); /* #374151 */
  --primary-foreground: oklch(1 0 0); /* #ffffff */
  --secondary: oklch(0.569 0.196 264.05); /* #6366f1 */
  --secondary-foreground: oklch(1 0 0); /* #ffffff */
  --muted: oklch(0.985 0.002 286.38); /* #f8fafc */
  --muted-foreground: oklch(0.198 0.015 286.75); /* #1f2937 */
  --accent: oklch(0.569 0.196 264.05); /* #6366f1 */
  --accent-foreground: oklch(1 0 0); /* #ffffff */
  --destructive: oklch(0.577 0.245 27.325); /* #dc2626 */
  --destructive-foreground: oklch(1 0 0); /* #ffffff */
  --border: oklch(0.922 0.013 286.38); /* #e5e7eb */
  --input: oklch(0.985 0.002 286.38); /* #f8fafc */
  --ring: oklch(0.569 0.196 264.05 / 0.5); /* rgba(99, 102, 241, 0.5) */
  --chart-1: oklch(0.569 0.196 264.05); /* #6366f1 */
  --chart-2: oklch(0.577 0.245 27.325); /* #dc2626 */
  --chart-3: oklch(0.701 0.221 65.69); /* #f59e0b */
  --chart-4: oklch(0.505 0.233 343.43); /* #be123c */
  --chart-5: oklch(0.748 0.135 164.75); /* #34d399 */
  --radius: 0.5rem;
  --sidebar: oklch(0.985 0.002 286.38); /* #f8fafc */
  --sidebar-foreground: oklch(0.278 0.013 286.62); /* #374151 */
  --sidebar-primary: oklch(0.569 0.196 264.05); /* #6366f1 */
  --sidebar-primary-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-accent: oklch(0.577 0.245 27.325); /* #dc2626 */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-border: oklch(0.922 0.013 286.38); /* #e5e7eb */
  --sidebar-ring: oklch(0.569 0.196 264.05 / 0.5); /* rgba(99, 102, 241, 0.5) */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.569 0.196 264.05);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.569 0.196 264.05);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
