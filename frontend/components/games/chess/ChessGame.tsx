'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Chessboard } from 'react-chessboard';
import { Chess, Square } from 'chess.js';
import { useGameSocket } from '@/hooks/use-game-socket';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Users, Trophy, MessageSquare, DollarSign } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ChessGameProps {
  roomId?: string;
  onGameEnd?: (result: any) => void;
}

export default function ChessGame({ roomId, onGameEnd }: ChessGameProps) {
  const {
    isConnected,
    isConnecting,
    error,
    currentRoom,
    gameState,
    chatMessages,
    chess,
    isMyTurn,
    myColor,
    connect,
    createRoom,
    joinRoom,
    makeMove,
    sendMessage,
    resign,
    offerDraw
  } = useGameSocket({ autoConnect: true });

  const [message, setMessage] = useState('');
  const [selectedSquare, setSelectedSquare] = useState<Square | null>(null);
  const [possibleMoves, setPossibleMoves] = useState<string[]>([]);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [gameSettings, setGameSettings] = useState({
    timeMinutes: 10,
    increment: 0,
    stakeAmount: 0
  });

  // Handle piece selection
  const handleSquareClick = useCallback((square: Square) => {
    if (!isMyTurn || !gameState || gameState.game_over) return;

    // If no square is selected, select this square if it has our piece
    if (!selectedSquare) {
      const piece = chess.get(square);
      if (piece && piece.color === (myColor === 'white' ? 'w' : 'b')) {
        setSelectedSquare(square);
        // Get possible moves for this piece
        const moves = chess.moves({ square, verbose: true });
        setPossibleMoves(moves.map(m => m.to));
      }
    } else {
      // If a square is already selected, try to move
      if (possibleMoves.includes(square)) {
        handleMove(selectedSquare, square);
      } else {
        // Select new piece if it's ours
        const piece = chess.get(square);
        if (piece && piece.color === (myColor === 'white' ? 'w' : 'b')) {
          setSelectedSquare(square);
          const moves = chess.moves({ square, verbose: true });
          setPossibleMoves(moves.map(m => m.to));
        } else {
          setSelectedSquare(null);
          setPossibleMoves([]);
        }
      }
    }
  }, [selectedSquare, possibleMoves, isMyTurn, gameState, chess, myColor]);

  // Handle piece move
  const handleMove = async (from: string, to: string) => {
    try {
      // Check for pawn promotion
      const piece = chess.get(from as Square);
      let promotion = undefined;

      if (piece?.type === 'p') {
        const toRank = to[1];
        if ((piece.color === 'w' && toRank === '8') || (piece.color === 'b' && toRank === '1')) {
          // For simplicity, auto-promote to queen. In a real game, you'd show a dialog
          promotion = 'q';
        }
      }

      await makeMove(from, to, promotion);
      setSelectedSquare(null);
      setPossibleMoves([]);
    } catch (error) {
      console.error('Move error:', error);
      setSelectedSquare(null);
      setPossibleMoves([]);
    }
  };

  // Handle drag and drop
  const onDrop = (sourceSquare: Square, targetSquare: Square) => {
    if (!isMyTurn || !gameState || gameState.game_over) return false;

    handleMove(sourceSquare, targetSquare);
    return true;
  };

  // Create new room
  const handleCreateRoom = async () => {
    setIsCreatingRoom(true);
    try {
      const result = await createRoom({
        game_type: 'chess',
        time_minutes: gameSettings.timeMinutes,
        increment: gameSettings.increment,
        stake_amount: gameSettings.stakeAmount,
        currency: 'USD',
        allow_spectators: true
      });
      console.log('Room created:', result.room_id);
    } catch (error) {
      console.error('Failed to create room:', error);
    } finally {
      setIsCreatingRoom(false);
    }
  };

  // Join existing room
  const handleJoinRoom = async () => {
    if (roomId) {
      try {
        await joinRoom(roomId);
      } catch (error) {
        console.error('Failed to join room:', error);
      }
    }
  };

  // Send chat message
  const handleSendMessage = () => {
    if (message.trim()) {
      sendMessage(message);
      setMessage('');
    }
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Auto-join room if provided
  useEffect(() => {
    if (roomId && isConnected && !currentRoom) {
      handleJoinRoom();
    }
  }, [roomId, isConnected, currentRoom]);

  // Handle game end
  useEffect(() => {
    if (gameState?.game_over && onGameEnd) {
      onGameEnd({
        winner: gameState.winner,
        result: gameState.result
      });
    }
  }, [gameState?.game_over]);

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Connection error: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  if (isConnecting) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Connecting to game server...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentRoom) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Create or Join a Chess Game</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Create New Game</h3>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div>
                <label className="text-sm font-medium">Time (minutes)</label>
                <Input
                  type="number"
                  value={gameSettings.timeMinutes}
                  onChange={(e) => setGameSettings(prev => ({ ...prev, timeMinutes: parseInt(e.target.value) || 10 }))}
                  min={1}
                  max={180}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Increment (seconds)</label>
                <Input
                  type="number"
                  value={gameSettings.increment}
                  onChange={(e) => setGameSettings(prev => ({ ...prev, increment: parseInt(e.target.value) || 0 }))}
                  min={0}
                  max={60}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Stake ($)</label>
                <Input
                  type="number"
                  value={gameSettings.stakeAmount}
                  onChange={(e) => setGameSettings(prev => ({ ...prev, stakeAmount: parseFloat(e.target.value) || 0 }))}
                  min={0}
                  step={0.01}
                />
              </div>
            </div>
            <Button onClick={handleCreateRoom} disabled={isCreatingRoom} className="w-full">
              {isCreatingRoom ? 'Creating...' : 'Create Game Room'}
            </Button>
          </div>

          {roomId && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Join Existing Game</h3>
              <Button onClick={handleJoinRoom} className="w-full">
                Join Room {roomId}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
      {/* Chess Board */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Chess Game</CardTitle>
              <div className="flex gap-2">
                {gameState && !gameState.game_over && (
                  <>
                    <Badge variant={gameState.turn === myColor ? 'default' : 'secondary'}>
                      {isMyTurn ? 'Your Turn' : "Opponent's Turn"}
                    </Badge>
                    <Badge variant="outline">
                      Playing as {myColor}
                    </Badge>
                  </>
                )}
                {gameState?.game_over && (
                  <Badge variant="destructive">
                    Game Over - {gameState.result}
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Chessboard
                position={gameState?.board_fen || chess.fen()}
                onPieceDrop={onDrop}
                onSquareClick={handleSquareClick}
                boardOrientation={myColor || 'white'}
                customSquareStyles={{
                  ...(selectedSquare && {
                    [selectedSquare]: {
                      backgroundColor: 'rgba(255, 255, 0, 0.4)'
                    }
                  }),
                  ...possibleMoves.reduce((acc, move) => ({
                    ...acc,
                    [move]: {
                      background: 'radial-gradient(circle, rgba(0,0,0,.1) 25%, transparent 25%)',
                      borderRadius: '50%'
                    }
                  }), {})
                }}
                arePiecesDraggable={isMyTurn && !gameState?.game_over}
              />
            </div>

            {/* Time Controls */}
            {gameState && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="flex items-center justify-center p-2 bg-secondary rounded">
                  <Clock className="w-4 h-4 mr-2" />
                  <span className="font-mono">
                    White: {formatTime(gameState.white_time)}
                  </span>
                </div>
                <div className="flex items-center justify-center p-2 bg-secondary rounded">
                  <Clock className="w-4 h-4 mr-2" />
                  <span className="font-mono">
                    Black: {formatTime(gameState.black_time)}
                  </span>
                </div>
              </div>
            )}

            {/* Game Actions */}
            {gameState && !gameState.game_over && myColor && (
              <div className="flex gap-2 mt-4">
                <Button variant="destructive" onClick={resign}>
                  Resign
                </Button>
                <Button variant="outline" onClick={offerDraw}>
                  Offer Draw
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Side Panel */}
      <div className="space-y-4">
        {/* Game Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Game Info</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Room ID</span>
              <Badge variant="outline">{currentRoom?.slice(0, 8)}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Move</span>
              <span className="font-mono">{gameState?.move_number || 0}</span>
            </div>
            {gameState && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Stake</span>
                <span className="flex items-center">
                  <DollarSign className="w-3 h-3 mr-1" />
                  {gameState.stake_amount || 0}
                </span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Chat and Spectators */}
        <Card className="h-[400px]">
          <CardHeader>
            <Tabs defaultValue="chat" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="chat">
                  <MessageSquare className="w-4 h-4 mr-1" />
                  Chat
                </TabsTrigger>
                <TabsTrigger value="spectators">
                  <Users className="w-4 h-4 mr-1" />
                  Spectators
                </TabsTrigger>
              </TabsList>

              <TabsContent value="chat" className="mt-4">
                <div className="flex flex-col h-[280px]">
                  <ScrollArea className="flex-1 p-2">
                    {chatMessages.map((msg, idx) => (
                      <div key={idx} className="mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant={msg.is_player ? 'default' : 'secondary'} className="text-xs">
                            {msg.is_player ? 'Player' : 'Spectator'}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {new Date(msg.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm mt-1">{msg.message}</p>
                      </div>
                    ))}
                  </ScrollArea>
                  <div className="flex gap-2 mt-2">
                    <Input
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      placeholder="Type a message..."
                      className="flex-1"
                    />
                    <Button onClick={handleSendMessage} size="sm">
                      Send
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="spectators" className="mt-4">
                <div className="text-center text-muted-foreground">
                  <Users className="w-8 h-8 mx-auto mb-2" />
                  <p>Spectator features coming soon!</p>
                </div>
              </TabsContent>
            </Tabs>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}