import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON>eistSans } from "geist/font/sans"
import { <PERSON>eistMono } from "geist/font/mono"
import { Analytics } from "@vercel/analytics/next"
import { ClerkProvider } from '@clerk/nextjs'
import { ConditionalLayout } from "@/components/layout/conditional-layout"
import { Suspense } from "react"
import "./globals.css"

export const metadata: Metadata = {
  title: "BetBet - Peer-to-Peer Betting & Gaming Platform",
  description:
    "The ultimate peer-to-peer betting and gaming platform. Play games, create markets, analyze data, and connect with the community.",
  generator: "v0.app",
  icons: {
    icon: [
      { url: '/favicon/favicon.ico' },
      { url: '/favicon/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    apple: [
      { url: '/favicon/apple-touch-icon.png' },
      { url: '/favicon/apple-touch-icon-57x57.png', sizes: '57x57' },
      { url: '/favicon/apple-touch-icon-72x72.png', sizes: '72x72' },
      { url: '/favicon/apple-touch-icon-76x76.png', sizes: '76x76' },
      { url: '/favicon/apple-touch-icon-114x114.png', sizes: '114x114' },
      { url: '/favicon/apple-touch-icon-120x120.png', sizes: '120x120' },
      { url: '/favicon/apple-touch-icon-144x144.png', sizes: '144x144' },
      { url: '/favicon/apple-touch-icon-152x152.png', sizes: '152x152' },
      { url: '/favicon/apple-touch-icon-180x180.png', sizes: '180x180' },
    ],
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}>
          <ConditionalLayout>
            {children}
          </ConditionalLayout>
          <Analytics />
        </body>
      </html>
    </ClerkProvider>
  )
}
