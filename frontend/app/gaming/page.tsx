import Link from "next/link"
import { GameDiscovery } from "@/components/gaming/game-discovery"
import { QuickPlaySection } from "@/components/gaming/quick-play-section"
import { TournamentSection } from "@/components/gaming/tournament-section"
import { FeaturedMatches } from "@/components/gaming/featured-matches"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function GamingPage() {
  return (
    <div className="container py-8 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Gaming Hub</h1>
        <p className="text-muted-foreground text-lg">Play games, host tournaments, and earn from your skills</p>
      </div>

      {/* Quick Access to Chess */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ♛ Chess
            </CardTitle>
            <CardDescription>Play real-time chess with wagering</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/gaming/chess">
              <Button className="w-full">
                Play Chess
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="opacity-60">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔴 Checkers
            </CardTitle>
            <CardDescription>Classic checkers gameplay</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" disabled className="w-full">
              Coming Soon
            </Button>
          </CardContent>
        </Card>

        <Card className="opacity-60">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🃏 Poker
            </CardTitle>
            <CardDescription>Texas Hold'em tournaments</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" disabled className="w-full">
              Coming Soon
            </Button>
          </CardContent>
        </Card>

        <Card className="opacity-60">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🎯 More Games
            </CardTitle>
            <CardDescription>Explore all available games</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" disabled className="w-full">
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </div>

      <FeaturedMatches />
      <QuickPlaySection />
      <GameDiscovery />
      <TournamentSection />
    </div>
  )
}
