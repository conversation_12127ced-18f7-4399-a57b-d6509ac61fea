#!/usr/bin/env python3

import asyncio
import sys
sys.path.append('../..')
from shared.models import Game
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

async def main():
    DATABASE_URL = "postgresql+asyncpg://postgres:123Bubblegums@localhost:5432/betbet_main"
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as db:
        games_data = [
            {
                "name": "Chess - Blitz",
                "category": "Board Games",
                "subcategory": "Chess",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Quick chess games between 3-10 minutes per player",
                    "time_control": {"base_minutes": 5, "increment_seconds": 0},
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-blitz.png"
            },
            {
                "name": "Chess - Rapid",
                "category": "Board Games",
                "subcategory": "Chess",
                "game_type": "realtime",
                "min_players": 2,
                "max_players": 2,
                "rules": {
                    "description": "Medium-paced chess with 10-60 minutes per player",
                    "time_control": {"base_minutes": 15, "increment_seconds": 10},
                    "features": ["timed_moves", "real_time", "wagering", "spectator_betting"]
                },
                "thumbnail_url": "/images/chess-rapid.png"
            }
        ]

        for game_data in games_data:
            game = Game(**game_data)
            db.add(game)

        await db.commit()
        print(f"✅ Successfully seeded {len(games_data)} games!")

        # Print game IDs
        from sqlalchemy import select
        result = await db.execute(select(Game))
        games = result.scalars().all()

        print("\n📋 Available games:")
        for game in games:
            print(f"• {game.name} (ID: {game.id})")

if __name__ == "__main__":
    asyncio.run(main())