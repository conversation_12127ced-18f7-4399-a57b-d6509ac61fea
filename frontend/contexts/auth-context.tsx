'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  username: string;
  email: string;
  displayName?: string;
  avatarUrl?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, username: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored auth token on mount
    const token = localStorage.getItem('auth-token');
    if (token) {
      // In a real app, verify token with backend
      // For now, simulate checking stored user data
      const storedUser = localStorage.getItem('user-data');
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser));
        } catch (error) {
          console.error('Failed to parse stored user data:', error);
          localStorage.removeItem('auth-token');
          localStorage.removeItem('user-data');
        }
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // Mock authentication - replace with actual API call
      const response = await fetch('http://localhost:8000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (response.ok) {
        const data = await response.json();
        const userData: User = {
          id: data.user_id || 'mock-user-id',
          username: data.username || email.split('@')[0],
          email: email,
          displayName: data.display_name || email.split('@')[0],
        };

        setUser(userData);
        localStorage.setItem('auth-token', data.token || 'mock-token');
        localStorage.setItem('user-data', JSON.stringify(userData));
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Login failed');
      }
    } catch (error) {
      // Mock successful login for development
      console.warn('Using mock authentication:', error);
      const userData: User = {
        id: 'mock-user-id',
        username: email.split('@')[0],
        email: email,
        displayName: email.split('@')[0],
      };

      setUser(userData);
      localStorage.setItem('auth-token', 'mock-token');
      localStorage.setItem('user-data', JSON.stringify(userData));
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (email: string, password: string, username: string) => {
    setIsLoading(true);
    try {
      // Mock signup - replace with actual API call
      const response = await fetch('http://localhost:8000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, username }),
      });

      if (response.ok) {
        const data = await response.json();
        const userData: User = {
          id: data.user_id || 'mock-user-id',
          username: username,
          email: email,
          displayName: username,
        };

        setUser(userData);
        localStorage.setItem('auth-token', data.token || 'mock-token');
        localStorage.setItem('user-data', JSON.stringify(userData));
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Signup failed');
      }
    } catch (error) {
      // Mock successful signup for development
      console.warn('Using mock authentication:', error);
      const userData: User = {
        id: 'mock-user-id',
        username: username,
        email: email,
        displayName: username,
      };

      setUser(userData);
      localStorage.setItem('auth-token', 'mock-token');
      localStorage.setItem('user-data', JSON.stringify(userData));
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('auth-token');
    localStorage.removeItem('user-data');
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    signup,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};