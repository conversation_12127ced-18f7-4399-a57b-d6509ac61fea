#!/bin/bash

# Kong Setup Script for BetBet Platform
# Configures all microservices through Kong Gateway

set -e

KONG_ADMIN_URL="http://localhost:8081"

echo "🚀 Setting up Kong API Gateway for BetBet Platform..."

# Wait for Kong to be ready
echo "⏳ Waiting for <PERSON> to be ready..."
until curl -f -s "$KONG_ADMIN_URL" > /dev/null; do
    echo "Waiting for Kong Admin API..."
    sleep 2
done

echo "✅ Kong is ready!"

# Function to create service and route
create_service_and_route() {
    local service_name=$1
    local service_url=$2
    local route_path=$3
    local route_port=$4

    echo "📡 Creating service: $service_name"

    # Create or update service
    curl -i -X POST $KONG_ADMIN_URL/services/ \
        --data "name=$service_name" \
        --data "url=$service_url" || \
    curl -i -X PATCH $KONG_ADMIN_URL/services/$service_name \
        --data "url=$service_url"

    echo "🛣️  Creating route for: $service_name"

    # Create route
    curl -i -X POST $KONG_ADMIN_URL/services/$service_name/routes \
        --data "paths[]=$route_path" \
        --data "strip_path=false" \
        --data "preserve_host=true"
}

# Function to add plugin to service
add_plugin() {
    local service_name=$1
    local plugin_name=$2
    local config=$3

    echo "🔌 Adding $plugin_name plugin to $service_name"

    curl -i -X POST $KONG_ADMIN_URL/services/$service_name/plugins \
        --data "name=$plugin_name" \
        $config
}

# ==============================================================================
# BetBet Services Configuration
# ==============================================================================

# 1. User Profile Service (Port 8000)
create_service_and_route "user-profile" "http://host.docker.internal:8000" "/api/v1/users" "8000"
create_service_and_route "user-profile-auth" "http://host.docker.internal:8000" "/api/v1/auth" "8000"

# 2. Game Engine Service (Port 8001)
create_service_and_route "game-engine" "http://host.docker.internal:8001" "/api/v1/games" "8001"
create_service_and_route "game-sessions" "http://host.docker.internal:8001" "/api/v1/sessions" "8001"

# 3. Betting Market Service (Port 8002)
create_service_and_route "betting-market" "http://host.docker.internal:8002" "/api/v1/markets" "8002"
create_service_and_route "betting-bets" "http://host.docker.internal:8002" "/api/v1/bets" "8002"

# 4. Expert Analysis Service (Port 8003)
create_service_and_route "expert-analysis" "http://host.docker.internal:8003" "/api/v1/analysis" "8003"
create_service_and_route "expert-reports" "http://host.docker.internal:8003" "/api/v1/reports" "8003"

# 5. Multi-Currency Wallet Service (Port 8004)
create_service_and_route "wallet" "http://host.docker.internal:8004" "/api/v1/wallets" "8004"
create_service_and_route "wallet-transactions" "http://host.docker.internal:8004" "/api/v1/transactions" "8004"
create_service_and_route "wallet-deposits" "http://host.docker.internal:8004" "/api/v1/deposits" "8004"
create_service_and_route "wallet-withdrawals" "http://host.docker.internal:8004" "/api/v1/withdrawals" "8004"
create_service_and_route "wallet-transfers" "http://host.docker.internal:8004" "/api/v1/transfers" "8004"

# 6. Social Forum Service (Port 8005)
create_service_and_route "social-communities" "http://host.docker.internal:8005" "/api/v1/communities" "8005"
create_service_and_route "social-threads" "http://host.docker.internal:8005" "/api/v1/threads" "8005"
create_service_and_route "social-groups" "http://host.docker.internal:8005" "/api/v1/groups" "8005"

# ==============================================================================
# Kong Plugins Configuration
# ==============================================================================

echo "🔌 Configuring Kong Plugins..."

# Add CORS plugin globally
echo "🌐 Adding CORS plugin..."
curl -i -X POST $KONG_ADMIN_URL/plugins/ \
    --data "name=cors" \
    --data "config.origins=*" \
    --data "config.methods=GET,POST,PUT,DELETE,PATCH,OPTIONS" \
    --data "config.headers=Accept,Accept-Version,Content-Length,Content-MD5,Content-Type,Date,X-Auth-Token,Authorization,X-Clerk-Auth" \
    --data "config.exposed_headers=X-Auth-Token" \
    --data "config.credentials=true" \
    --data "config.max_age=3600"

# Add Rate Limiting plugin globally
echo "🚦 Adding Rate Limiting plugin..."
curl -i -X POST $KONG_ADMIN_URL/plugins/ \
    --data "name=rate-limiting" \
    --data "config.minute=1000" \
    --data "config.hour=10000" \
    --data "config.policy=local"

# Add Request Size Limiting plugin
echo "📏 Adding Request Size Limiting plugin..."
curl -i -X POST $KONG_ADMIN_URL/plugins/ \
    --data "name=request-size-limiting" \
    --data "config.allowed_payload_size=10"

# Add Response Transformer for API versioning
echo "🔄 Adding Response Transformer plugin..."
curl -i -X POST $KONG_ADMIN_URL/plugins/ \
    --data "name=response-transformer" \
    --data "config.add.headers=X-BetBet-Version:1.0.0,X-Powered-By:BetBet-Platform"

# Add Prometheus plugin for monitoring
echo "📊 Adding Prometheus plugin..."
curl -i -X POST $KONG_ADMIN_URL/plugins/ \
    --data "name=prometheus"

# Add specific authentication plugins for sensitive services
echo "🔐 Adding authentication plugins to wallet service..."
add_plugin "wallet" "key-auth" "--data config.key_names=X-API-Key"
add_plugin "wallet-transactions" "key-auth" "--data config.key_names=X-API-Key"
add_plugin "wallet-deposits" "key-auth" "--data config.key_names=X-API-Key"
add_plugin "wallet-withdrawals" "key-auth" "--data config.key_names=X-API-Key"
add_plugin "wallet-transfers" "key-auth" "--data config.key_names=X-API-Key"

echo "🔐 Adding authentication plugins to user profile service..."
add_plugin "user-profile" "key-auth" "--data config.key_names=X-API-Key"

# ==============================================================================
# Health Check Routes
# ==============================================================================

echo "🏥 Setting up health check routes..."

# Create health check service
curl -i -X POST $KONG_ADMIN_URL/services/ \
    --data "name=health-check" \
    --data "url=http://host.docker.internal:8000"

curl -i -X POST $KONG_ADMIN_URL/services/health-check/routes \
    --data "paths[]=/health" \
    --data "strip_path=false"

# ==============================================================================
# API Documentation Route
# ==============================================================================

echo "📚 Setting up API documentation route..."

# Create docs service pointing to any service for now
curl -i -X POST $KONG_ADMIN_URL/services/ \
    --data "name=api-docs" \
    --data "url=http://host.docker.internal:8000"

curl -i -X POST $KONG_ADMIN_URL/services/api-docs/routes \
    --data "paths[]=/docs" \
    --data "strip_path=false"

echo ""
echo "🎉 Kong API Gateway setup completed successfully!"
echo ""
echo "📋 BetBet Platform API Endpoints (via Kong Gateway):"
echo "=================================================="
echo "🌐 Kong Proxy (API Gateway): http://localhost:8080"
echo "⚙️  Kong Admin API: http://localhost:8001"
echo "🖥️  Kong Manager (GUI): http://localhost:8002"
echo "📊 Konga Admin UI: http://localhost:1337"
echo ""
echo "🔗 Service Routes through Kong Gateway:"
echo "• User Profile: http://localhost:8080/api/v1/users/*"
echo "• Authentication: http://localhost:8080/api/v1/auth/*"
echo "• Game Engine: http://localhost:8080/api/v1/games/*"
echo "• Game Sessions: http://localhost:8080/api/v1/sessions/*"
echo "• Betting Markets: http://localhost:8080/api/v1/markets/*"
echo "• Betting Bets: http://localhost:8080/api/v1/bets/*"
echo "• Expert Analysis: http://localhost:8080/api/v1/analysis/*"
echo "• Expert Reports: http://localhost:8080/api/v1/reports/*"
echo "• Wallets: http://localhost:8080/api/v1/wallets/*"
echo "• Transactions: http://localhost:8080/api/v1/transactions/*"
echo "• Deposits: http://localhost:8080/api/v1/deposits/*"
echo "• Withdrawals: http://localhost:8080/api/v1/withdrawals/*"
echo "• P2P Transfers: http://localhost:8080/api/v1/transfers/*"
echo "• Communities: http://localhost:8080/api/v1/communities/*"
echo "• Threads: http://localhost:8080/api/v1/threads/*"
echo "• Groups: http://localhost:8080/api/v1/groups/*"
echo ""
echo "🏥 Health Check: http://localhost:8080/health"
echo "📚 API Docs: http://localhost:8080/docs"
echo ""
echo "✅ All BetBet microservices are now accessible through Kong Gateway!"