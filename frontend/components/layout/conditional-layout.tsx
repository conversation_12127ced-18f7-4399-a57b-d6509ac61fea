"use client"

import { useAuth } from "@clerk/nextjs"
import { usePathname, useRouter } from "next/navigation"
import { SidebarNav } from "@/components/layout/sidebar-nav"
import { TopNavbar } from "@/components/layout/top-navbar"
import { ContentWrapper } from "@/components/layout/content-wrapper"
import { Suspense, useEffect } from "react"

const protectedRoutes = [
  '/gaming',
  '/wallet',
  '/betting',
  '/analysis',
  '/forum',
  '/dashboard'
]

export function ConditionalLayout({ children }: { children: React.ReactNode }) {
  const { isSignedIn, isLoaded } = useAuth()
  const pathname = usePathname()
  const router = useRouter()

  // Check if current route is protected
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))

  // Handle redirects based on authentication status
  useEffect(() => {
    if (isLoaded) {
      // Redirect unauthenticated users from protected routes to landing page
      if (!isSignedIn && isProtectedRoute) {
        router.push('/')
      }
      // Redirect authenticated users from landing page to dashboard
      else if (isSignedIn && pathname === '/') {
        router.push('/dashboard')
      }
    }
  }, [isLoaded, isSignedIn, isProtectedRoute, router, pathname])

  // Show loading state while Clerk is loading
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-pulse text-lg">Loading...</div>
        </div>
      </div>
    )
  }

  // Don't render protected content for unauthenticated users OR authenticated users on home page
  if ((!isSignedIn && isProtectedRoute) || (isSignedIn && pathname === '/')) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-pulse text-lg">Redirecting...</div>
        </div>
      </div>
    )
  }

  // For authenticated users, show the full layout with navigation
  if (isSignedIn) {
    return (
      <>
        <Suspense fallback={<div>Loading navigation...</div>}>
          <SidebarNav />
          <TopNavbar />
        </Suspense>
        <main className="md:ml-64 pt-16">
          <ContentWrapper>{children}</ContentWrapper>
        </main>
      </>
    )
  }

  // For unauthenticated users, show content without navigation
  return (
    <main className="min-h-screen">
      {children}
    </main>
  )
}