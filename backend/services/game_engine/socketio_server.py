"""
BetBet Socket.IO Server for Real-Time Gaming
Handles game rooms, spectators, and real-time synchronization
"""

import asyncio
import json
from typing import Dict, List, Optional, Set, Any
from datetime import datetime
import socketio
import uuid

from chess_engine import ChessGame, ChessTimeControl, chess_manager
from wagering import wagering_manager


class GameRoom:
    """Represents a game room with players and spectators"""

    def __init__(self, room_id: str, game_type: str = "chess"):
        self.room_id = room_id
        self.game_type = game_type
        self.players: Dict[str, str] = {}  # player_id -> sid
        self.spectators: Set[str] = set()  # Set of spectator sids
        self.game = None
        self.created_at = datetime.utcnow()
        self.state = "waiting"  # waiting, playing, finished
        self.chat_history: List[Dict] = []
        self.spectator_bets: Dict[str, Dict] = {}

    def add_player(self, player_id: str, sid: str) -> bool:
        """Add a player to the room"""
        if len(self.players) >= 2:
            return False
        self.players[player_id] = sid
        if len(self.players) == 2:
            self.state = "ready"
        return True

    def add_spectator(self, sid: str):
        """Add a spectator to the room"""
        self.spectators.add(sid)

    def remove_spectator(self, sid: str):
        """Remove a spectator from the room"""
        self.spectators.discard(sid)

    def get_all_sids(self) -> List[str]:
        """Get all socket IDs in the room"""
        return list(self.players.values()) + list(self.spectators)


class GameSocketIOServer:
    """Socket.IO server for real-time game management"""

    def __init__(self, redis_url: str = "redis://localhost:6379"):
        # Create Socket.IO server (without Redis for now to avoid dependency issues)
        self.sio = socketio.AsyncServer(
            async_mode='asgi',
            cors_allowed_origins='*'
        )

        # Game rooms management
        self.rooms: Dict[str, GameRoom] = {}
        self.user_rooms: Dict[str, str] = {}  # user_id -> room_id
        self.sid_to_user: Dict[str, str] = {}  # sid -> user_id

        # Setup event handlers
        self.setup_handlers()

    def setup_handlers(self):
        """Setup Socket.IO event handlers"""

        @self.sio.event
        async def connect(sid, environ, auth):
            """Handle client connection"""
            print(f"Client connected: {sid}")

            # Verify authentication
            if not auth or 'token' not in auth:
                await self.sio.disconnect(sid)
                return False

            # TODO: Verify JWT token and get user_id
            user_id = auth.get('user_id')
            if not user_id:
                await self.sio.disconnect(sid)
                return False

            self.sid_to_user[sid] = user_id

            # Send available rooms
            await self.sio.emit('rooms_list', {
                'rooms': self.get_available_rooms()
            }, to=sid)

            return True

        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection"""
            print(f"Client disconnected: {sid}")

            # Clean up user data
            user_id = self.sid_to_user.get(sid)
            if user_id:
                # Handle player leaving game
                room_id = self.user_rooms.get(user_id)
                if room_id and room_id in self.rooms:
                    room = self.rooms[room_id]

                    # Remove from spectators
                    room.remove_spectator(sid)

                    # If player in game, pause the game
                    if user_id in room.players:
                        await self.handle_player_disconnect(room, user_id)

                # Clean up mappings
                del self.sid_to_user[sid]
                if user_id in self.user_rooms:
                    del self.user_rooms[user_id]

        @self.sio.event
        async def create_room(sid, data):
            """Create a new game room"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            # Create room with parameters
            room_id = str(uuid.uuid4())
            room = GameRoom(room_id, data.get('game_type', 'chess'))

            # Add creator as first player
            room.add_player(user_id, sid)

            # Store room
            self.rooms[room_id] = room
            self.user_rooms[user_id] = room_id

            # Join Socket.IO room
            await self.sio.enter_room(sid, room_id)

            # Initialize game based on type
            if room.game_type == 'chess':
                # Parse time control
                time_control = ChessTimeControl(
                    base_minutes=data.get('time_minutes', 10),
                    increment_seconds=data.get('increment', 0)
                )

                # Will initialize game when second player joins
                room.game_data = {
                    'time_control': time_control,
                    'stake_amount': data.get('stake_amount', 0),
                    'currency': data.get('currency', 'USD')
                }

            # Broadcast room creation
            await self.broadcast_rooms_update()

            return {
                'room_id': room_id,
                'status': 'waiting_for_opponent'
            }

        @self.sio.event
        async def join_room(sid, data):
            """Join an existing game room"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            room_id = data.get('room_id')
            if not room_id or room_id not in self.rooms:
                return {'error': 'Room not found'}

            room = self.rooms[room_id]

            # Check if joining as player or spectator
            join_as = data.get('join_as', 'player')

            if join_as == 'player':
                if not room.add_player(user_id, sid):
                    # Room full, join as spectator
                    room.add_spectator(sid)
                    await self.sio.enter_room(sid, room_id)
                    return {'status': 'joined_as_spectator'}

                self.user_rooms[user_id] = room_id
                await self.sio.enter_room(sid, room_id)

                # If room now has 2 players, start the game
                if len(room.players) == 2:
                    await self.start_game(room)

                return {'status': 'joined_as_player'}

            else:
                # Join as spectator
                room.add_spectator(sid)
                await self.sio.enter_room(sid, room_id)

                # Send current game state if game is ongoing
                if room.game and room.state == 'playing':
                    await self.sio.emit('game_state', {
                        'state': room.game.get_game_state()
                    }, to=sid)

                return {'status': 'joined_as_spectator'}

        @self.sio.event
        async def make_move(sid, data):
            """Handle a game move"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            room_id = self.user_rooms.get(user_id)
            if not room_id or room_id not in self.rooms:
                return {'error': 'Not in a game room'}

            room = self.rooms[room_id]

            if room.state != 'playing':
                return {'error': 'Game not in progress'}

            if room.game_type == 'chess':
                # Make chess move
                result = room.game.make_move(
                    data['from'],
                    data['to'],
                    user_id,
                    promotion=data.get('promotion')
                )

                if 'error' in result:
                    return result

                # Broadcast move to all in room
                await self.sio.emit('move_made', {
                    'player': user_id,
                    'move': result['move'],
                    'board_fen': result['board_fen'],
                    'game_state': result
                }, room=room_id)

                # Check for game end
                if result.get('game_over'):
                    await self.end_game(room, result)

                return {'status': 'move_accepted'}

        @self.sio.event
        async def send_message(sid, data):
            """Handle chat message"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            room_id = data.get('room_id')
            if not room_id or room_id not in self.rooms:
                return {'error': 'Room not found'}

            room = self.rooms[room_id]

            # Create message
            message = {
                'user_id': user_id,
                'message': data['message'],
                'timestamp': datetime.utcnow().isoformat(),
                'is_player': user_id in room.players
            }

            # Store in history
            room.chat_history.append(message)

            # Broadcast to room
            await self.sio.emit('new_message', message, room=room_id)

        @self.sio.event
        async def place_spectator_bet(sid, data):
            """Handle spectator betting on game outcome"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            room_id = data.get('room_id')
            if not room_id or room_id not in self.rooms:
                return {'error': 'Room not found'}

            room = self.rooms[room_id]

            if room.state != 'playing':
                return {'error': 'Game not in progress'}

            # Process bet
            bet = {
                'user_id': user_id,
                'amount': data['amount'],
                'prediction': data['prediction'],  # 'white', 'black', 'draw'
                'timestamp': datetime.utcnow().isoformat()
            }

            room.spectator_bets[user_id] = bet

            # Update total pool
            total_pool = sum(b['amount'] for b in room.spectator_bets.values())

            # Broadcast updated betting info
            await self.sio.emit('betting_update', {
                'total_pool': total_pool,
                'bet_counts': self.get_bet_distribution(room),
                'your_bet': bet
            }, room=room_id)

            return {'status': 'bet_placed'}

        @self.sio.event
        async def resign(sid):
            """Handle player resignation"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            room_id = self.user_rooms.get(user_id)
            if not room_id or room_id not in self.rooms:
                return {'error': 'Not in a game'}

            room = self.rooms[room_id]

            if room.state != 'playing':
                return {'error': 'Game not in progress'}

            # Process resignation
            if room.game:
                result = room.game.resign(user_id)
                await self.end_game(room, result)

            return {'status': 'resigned'}

        @self.sio.event
        async def request_draw(sid):
            """Handle draw request"""
            user_id = self.sid_to_user.get(sid)
            if not user_id:
                return {'error': 'Not authenticated'}

            room_id = self.user_rooms.get(user_id)
            if not room_id or room_id not in self.rooms:
                return {'error': 'Not in a game'}

            room = self.rooms[room_id]

            # Get opponent
            opponent_id = None
            for player_id in room.players:
                if player_id != user_id:
                    opponent_id = player_id
                    break

            if opponent_id and opponent_id in room.players:
                opponent_sid = room.players[opponent_id]
                await self.sio.emit('draw_offered', {
                    'from': user_id
                }, to=opponent_sid)

            return {'status': 'draw_offered'}

    async def start_game(self, room: GameRoom):
        """Start a game when both players are ready"""
        if room.game_type == 'chess':
            # Get player IDs
            player_ids = list(room.players.keys())

            # Create chess game
            room.game = ChessGame(
                session_id=room.room_id,
                white_player_id=player_ids[0],
                black_player_id=player_ids[1],
                time_control=room.game_data['time_control'],
                stake_amount=room.game_data['stake_amount'],
                currency=room.game_data['currency']
            )

            # Start the game
            game_state = room.game.start_game()

            # Update room state
            room.state = 'playing'

            # Broadcast game start
            await self.sio.emit('game_started', {
                'game_state': game_state,
                'players': {
                    'white': player_ids[0],
                    'black': player_ids[1]
                }
            }, room=room.room_id)

            # Start clock management
            asyncio.create_task(self.manage_game_clock(room))

    async def manage_game_clock(self, room: GameRoom):
        """Manage game clock for timed games"""
        while room.state == 'playing' and room.game:
            await asyncio.sleep(1)

            # Update clocks
            if room.game.update_clock():
                # Broadcast time update
                await self.sio.emit('clock_update', {
                    'white_time': room.game.time_control.white_time,
                    'black_time': room.game.time_control.black_time
                }, room=room.room_id)

                # Check for timeout
                if room.game.check_timeout():
                    result = room.game.handle_timeout()
                    await self.end_game(room, result)
                    break

    async def end_game(self, room: GameRoom, result: Dict):
        """Handle game ending"""
        room.state = 'finished'

        # Process spectator bets if any
        if room.spectator_bets:
            winners = self.process_spectator_payouts(room, result['winner'])
            result['spectator_winners'] = winners

        # Broadcast game end
        await self.sio.emit('game_ended', result, room=room.room_id)

        # Clean up room after delay
        asyncio.create_task(self.cleanup_room(room.room_id))

    async def cleanup_room(self, room_id: str, delay: int = 300):
        """Clean up room after delay (5 minutes)"""
        await asyncio.sleep(delay)

        if room_id in self.rooms:
            room = self.rooms[room_id]

            # Disconnect all users
            for sid in room.get_all_sids():
                await self.sio.leave_room(sid, room_id)

            # Remove room
            del self.rooms[room_id]

            # Broadcast updated room list
            await self.broadcast_rooms_update()

    async def handle_player_disconnect(self, room: GameRoom, user_id: str):
        """Handle player disconnection during game"""
        if room.state == 'playing':
            # Pause game and give player time to reconnect
            room.game.pause()

            # Notify other player
            for player_id, sid in room.players.items():
                if player_id != user_id:
                    await self.sio.emit('opponent_disconnected', {
                        'grace_period': 120  # 2 minutes to reconnect
                    }, to=sid)

            # Start reconnection timer
            asyncio.create_task(self.reconnection_timer(room, user_id))

    async def reconnection_timer(self, room: GameRoom, user_id: str, timeout: int = 120):
        """Give player time to reconnect"""
        await asyncio.sleep(timeout)

        # Check if player reconnected
        if user_id not in self.user_rooms or self.user_rooms[user_id] != room.room_id:
            # Player didn't reconnect, forfeit game
            if room.game:
                result = room.game.forfeit(user_id)
                await self.end_game(room, result)

    def get_available_rooms(self) -> List[Dict]:
        """Get list of available rooms"""
        available = []
        for room_id, room in self.rooms.items():
            if room.state == 'waiting':
                available.append({
                    'room_id': room_id,
                    'game_type': room.game_type,
                    'players': len(room.players),
                    'spectators': len(room.spectators),
                    'created_at': room.created_at.isoformat()
                })
        return available

    async def broadcast_rooms_update(self):
        """Broadcast updated room list to all connected clients"""
        await self.sio.emit('rooms_update', {
            'rooms': self.get_available_rooms()
        })

    def get_bet_distribution(self, room: GameRoom) -> Dict:
        """Get betting distribution for a room"""
        distribution = {'white': 0, 'black': 0, 'draw': 0}
        for bet in room.spectator_bets.values():
            prediction = bet['prediction']
            if prediction in distribution:
                distribution[prediction] += bet['amount']
        return distribution

    def process_spectator_payouts(self, room: GameRoom, winner: str) -> List[Dict]:
        """Process payouts for spectator bets"""
        winners = []
        total_pool = sum(b['amount'] for b in room.spectator_bets.values())

        # Find winning bets
        winning_bets = [
            bet for bet in room.spectator_bets.values()
            if bet['prediction'] == winner
        ]

        if winning_bets:
            winning_total = sum(b['amount'] for b in winning_bets)

            for bet in winning_bets:
                payout = (bet['amount'] / winning_total) * total_pool
                winners.append({
                    'user_id': bet['user_id'],
                    'payout': payout,
                    'profit': payout - bet['amount']
                })

        return winners


# Create global instance
game_server = GameSocketIOServer()
sio = game_server.sio