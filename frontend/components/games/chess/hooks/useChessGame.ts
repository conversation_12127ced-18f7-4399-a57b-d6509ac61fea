import { useState, useEffect, useCallback } from 'react';
import { ChessGameConfig, ChessGameResponse, ChessJoinResponse, ChessGameState } from '../types';

interface UseChessGameReturn {
  createGame: (config: ChessGameConfig) => Promise<ChessGameResponse | null>;
  joinGame: (sessionId: string) => Promise<ChessJoinResponse | null>;
  loading: boolean;
  error: string | null;
}

export const useChessGame = (): UseChessGameReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createGame = useCallback(async (config: ChessGameConfig): Promise<ChessGameResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/game_engine/chess/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}` // Adjust based on your auth
        },
        body: JSON.stringify(config)
      });

      if (!response.ok) {
        throw new Error(`Failed to create game: ${response.statusText}`);
      }

      const result: ChessGameResponse = await response.json();
      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const joinGame = useCallback(async (sessionId: string): Promise<ChessJoinResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/game_engine/chess/join/${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}` // Adjust based on your auth
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to join game: ${response.statusText}`);
      }

      const result: ChessJoinResponse = await response.json();
      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createGame,
    joinGame,
    loading,
    error
  };
};